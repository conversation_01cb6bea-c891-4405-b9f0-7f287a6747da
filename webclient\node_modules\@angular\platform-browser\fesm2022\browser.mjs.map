{"version": 3, "file": "browser.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/platform-browser/src/browser/browser_adapter.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/platform-browser/src/browser/testability.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/platform-browser/src/browser/xhr.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/platform-browser/src/dom/events/dom_events.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/platform-browser/src/dom/events/key_events.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/platform-browser/src/browser.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ɵparseCookieValue as parseCookieValue,\n  ɵsetRootDomAdapter as setRootDomAdapter,\n  ɵDomAdapter as DomAdapter,\n} from '@angular/common';\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nexport class BrowserDomAdapter extends DomAdapter {\n  override readonly supportsDOMEvents: boolean = true;\n\n  static makeCurrent() {\n    setRootDomAdapter(new BrowserDomAdapter());\n  }\n\n  override onAndCancel(el: Node, evt: any, listener: any, options: any): Function {\n    el.addEventListener(evt, listener, options);\n    return () => {\n      el.removeEventListener(evt, listener, options);\n    };\n  }\n  override dispatchEvent(el: Node, evt: any) {\n    el.dispatchEvent(evt);\n  }\n  override remove(node: Node): void {\n    (node as Element | Text | Comment).remove();\n  }\n  override createElement(tagName: string, doc?: Document): HTMLElement {\n    doc = doc || this.getDefaultDocument();\n    return doc.createElement(tagName);\n  }\n  override createHtmlDocument(): Document {\n    return document.implementation.createHTMLDocument('fakeTitle');\n  }\n  override getDefaultDocument(): Document {\n    return document;\n  }\n\n  override isElementNode(node: Node): boolean {\n    return node.nodeType === Node.ELEMENT_NODE;\n  }\n\n  override isShadowRoot(node: any): boolean {\n    return node instanceof DocumentFragment;\n  }\n\n  /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n  override getGlobalEventTarget(doc: Document, target: string): EventTarget | null {\n    if (target === 'window') {\n      return window;\n    }\n    if (target === 'document') {\n      return doc;\n    }\n    if (target === 'body') {\n      return doc.body;\n    }\n    return null;\n  }\n  override getBaseHref(doc: Document): string | null {\n    const href = getBaseElementHref();\n    return href == null ? null : relativePath(href);\n  }\n  override resetBaseElement(): void {\n    baseElement = null;\n  }\n  override getUserAgent(): string {\n    return window.navigator.userAgent;\n  }\n  override getCookie(name: string): string | null {\n    return parseCookieValue(document.cookie, name);\n  }\n}\n\nlet baseElement: HTMLElement | null = null;\nfunction getBaseElementHref(): string | null {\n  baseElement = baseElement || document.head.querySelector('base');\n  return baseElement ? baseElement.getAttribute('href') : null;\n}\n\nfunction relativePath(url: string): string {\n  // The base URL doesn't really matter, we just need it so relative paths have something\n  // to resolve against. In the browser `HTMLBaseElement.href` is always absolute.\n  return new URL(url, document.baseURI).pathname;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ɵgetDOM as getDOM} from '@angular/common';\nimport {\n  GetTestability,\n  Testability,\n  TestabilityRegistry,\n  ɵglobal as global,\n  ɵRuntimeError as RuntimeError,\n} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../errors';\n\nexport class BrowserGetTestability implements GetTestability {\n  addToWindow(registry: TestabilityRegistry): void {\n    global['getAngularTestability'] = (elem: any, findInAncestors: boolean = true) => {\n      const testability = registry.findTestabilityInTree(elem, findInAncestors);\n      if (testability == null) {\n        throw new RuntimeError(\n          RuntimeErrorCode.TESTABILITY_NOT_FOUND,\n          (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            'Could not find testability for element.',\n        );\n      }\n      return testability;\n    };\n\n    global['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n\n    global['getAllAngularRootElements'] = () => registry.getAllRootElements();\n\n    const whenAllStable = (callback: () => void) => {\n      const testabilities = global['getAllAngularTestabilities']() as Testability[];\n      let count = testabilities.length;\n      const decrement = function () {\n        count--;\n        if (count == 0) {\n          callback();\n        }\n      };\n      testabilities.forEach((testability) => {\n        testability.whenStable(decrement);\n      });\n    };\n\n    if (!global['frameworkStabilizers']) {\n      global['frameworkStabilizers'] = [];\n    }\n    global['frameworkStabilizers'].push(whenAllStable);\n  }\n\n  findTestabilityInTree(\n    registry: TestabilityRegistry,\n    elem: any,\n    findInAncestors: boolean,\n  ): Testability | null {\n    if (elem == null) {\n      return null;\n    }\n    const t = registry.getTestability(elem);\n    if (t != null) {\n      return t;\n    } else if (!findInAncestors) {\n      return null;\n    }\n    if (getDOM().isShadowRoot(elem)) {\n      return this.findTestabilityInTree(registry, (<any>elem).host, true);\n    }\n    return this.findTestabilityInTree(registry, elem.parentElement, true);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {XhrFactory} from '@angular/common';\nimport {Injectable} from '@angular/core';\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\n@Injectable()\nexport class BrowserXhr implements XhrFactory {\n  build(): XMLHttpRequest {\n    return new XMLHttpRequest();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {Inject, Injectable, type ListenerOptions} from '@angular/core';\n\nimport {EventManagerPlugin} from './event_manager';\n\n@Injectable()\nexport class DomEventsPlugin extends EventManagerPlugin {\n  constructor(@Inject(DOCUMENT) doc: any) {\n    super(doc);\n  }\n\n  // This plugin should come last in the list of plugins, because it accepts all\n  // events.\n  override supports(eventName: string): boolean {\n    return true;\n  }\n\n  override addEventListener(\n    element: HTMLElement,\n    eventName: string,\n    handler: Function,\n    options?: ListenerOptions,\n  ): Function {\n    element.addEventListener(eventName, handler as EventListener, options);\n    return () => this.removeEventListener(element, eventName, handler as EventListener, options);\n  }\n\n  removeEventListener(\n    target: any,\n    eventName: string,\n    callback: Function,\n    options?: ListenerOptions,\n  ): void {\n    return target.removeEventListener(eventName, callback as EventListener, options);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT, ɵgetDOM as getDOM} from '@angular/common';\nimport {Inject, Injectable, type ListenerOptions, NgZone} from '@angular/core';\n\nimport {EventManagerPlugin} from './event_manager';\n\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\n\n// The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\nconst _keyMap: {[k: string]: string} = {\n  '\\b': 'Backspace',\n  '\\t': 'Tab',\n  '\\x7F': 'Delete',\n  '\\x1B': 'Escape',\n  'Del': 'Delete',\n  'Esc': 'Escape',\n  'Left': 'ArrowLeft',\n  'Right': 'ArrowRight',\n  'Up': 'ArrowUp',\n  'Down': 'ArrowDown',\n  'Menu': 'ContextMenu',\n  'Scroll': 'ScrollLock',\n  'Win': 'OS',\n};\n\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS: {[key: string]: (event: KeyboardEvent) => boolean} = {\n  'alt': (event: KeyboardEvent) => event.altKey,\n  'control': (event: KeyboardEvent) => event.ctrlKey,\n  'meta': (event: KeyboardEvent) => event.metaKey,\n  'shift': (event: KeyboardEvent) => event.shiftKey,\n};\n\n/**\n * A browser plug-in that provides support for handling of key events in Angular.\n */\n@Injectable()\nexport class KeyEventsPlugin extends EventManagerPlugin {\n  /**\n   * Initializes an instance of the browser plug-in.\n   * @param doc The document in which key events will be detected.\n   */\n  constructor(@Inject(DOCUMENT) doc: any) {\n    super(doc);\n  }\n\n  /**\n   * Reports whether a named key event is supported.\n   * @param eventName The event name to query.\n   * @return True if the named key event is supported.\n   */\n  override supports(eventName: string): boolean {\n    return KeyEventsPlugin.parseEventName(eventName) != null;\n  }\n\n  /**\n   * Registers a handler for a specific element and key event.\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the key event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns The key event that was registered.\n   */\n  override addEventListener(\n    element: HTMLElement,\n    eventName: string,\n    handler: Function,\n    options?: ListenerOptions,\n  ): Function {\n    const parsedEvent = KeyEventsPlugin.parseEventName(eventName)!;\n\n    const outsideHandler = KeyEventsPlugin.eventCallback(\n      parsedEvent['fullKey'],\n      handler,\n      this.manager.getZone(),\n    );\n\n    return this.manager.getZone().runOutsideAngular(() => {\n      return getDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler, options);\n    });\n  }\n\n  /**\n   * Parses the user provided full keyboard event definition and normalizes it for\n   * later internal use. It ensures the string is all lowercase, converts special\n   * characters to a standard spelling, and orders all the values consistently.\n   *\n   * @param eventName The name of the key event to listen for.\n   * @returns an object with the full, normalized string, and the dom event name\n   * or null in the case when the event doesn't match a keyboard event.\n   */\n  static parseEventName(eventName: string): {fullKey: string; domEventName: string} | null {\n    const parts: string[] = eventName.toLowerCase().split('.');\n\n    const domEventName = parts.shift();\n    if (parts.length === 0 || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n      return null;\n    }\n\n    const key = KeyEventsPlugin._normalizeKey(parts.pop()!);\n\n    let fullKey = '';\n    let codeIX = parts.indexOf('code');\n    if (codeIX > -1) {\n      parts.splice(codeIX, 1);\n      fullKey = 'code.';\n    }\n    MODIFIER_KEYS.forEach((modifierName) => {\n      const index: number = parts.indexOf(modifierName);\n      if (index > -1) {\n        parts.splice(index, 1);\n        fullKey += modifierName + '.';\n      }\n    });\n    fullKey += key;\n\n    if (parts.length != 0 || key.length === 0) {\n      // returning null instead of throwing to let another plugin process the event\n      return null;\n    }\n\n    // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n    //       The code must remain in the `result['domEventName']` form.\n    // return {domEventName, fullKey};\n    const result: {fullKey: string; domEventName: string} = {} as any;\n    result['domEventName'] = domEventName;\n    result['fullKey'] = fullKey;\n    return result;\n  }\n\n  /**\n   * Determines whether the actual keys pressed match the configured key code string.\n   * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n   * event is attached to the DOM during the `addEventListener` call. This is unseen\n   * by the end user and is normalized for internal consistency and parsing.\n   *\n   * @param event The keyboard event.\n   * @param fullKeyCode The normalized user defined expected key event string\n   * @returns boolean.\n   */\n  static matchEventFullKeyCode(event: KeyboardEvent, fullKeyCode: string): boolean {\n    let keycode = _keyMap[event.key] || event.key;\n    let key = '';\n    if (fullKeyCode.indexOf('code.') > -1) {\n      keycode = event.code;\n      key = 'code.';\n    }\n    // the keycode could be unidentified so we have to check here\n    if (keycode == null || !keycode) return false;\n    keycode = keycode.toLowerCase();\n    if (keycode === ' ') {\n      keycode = 'space'; // for readability\n    } else if (keycode === '.') {\n      keycode = 'dot'; // because '.' is used as a separator in event names\n    }\n    MODIFIER_KEYS.forEach((modifierName) => {\n      if (modifierName !== keycode) {\n        const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n        if (modifierGetter(event)) {\n          key += modifierName + '.';\n        }\n      }\n    });\n    key += keycode;\n    return key === fullKeyCode;\n  }\n\n  /**\n   * Configures a handler callback for a key event.\n   * @param fullKey The event name that combines all simultaneous keystrokes.\n   * @param handler The function that responds to the key event.\n   * @param zone The zone in which the event occurred.\n   * @returns A callback function.\n   */\n  static eventCallback(fullKey: string, handler: Function, zone: NgZone): Function {\n    return (event: KeyboardEvent) => {\n      if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n        zone.runGuarded(() => handler(event));\n      }\n    };\n  }\n\n  /** @internal */\n  static _normalizeKey(keyName: string): string {\n    return keyName === 'esc' ? 'escape' : keyName;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  CommonModule,\n  DOCUMENT,\n  XhrFactory,\n  ɵPLATFORM_BROWSER_ID as PLATFORM_BROWSER_ID,\n} from '@angular/common';\nimport {\n  ApplicationConfig as ApplicationConfigFromCore,\n  ApplicationModule,\n  ApplicationRef,\n  createPlatformFactory,\n  ErrorHandler,\n  InjectionToken,\n  NgModule,\n  NgZone,\n  PLATFORM_ID,\n  PLATFORM_INITIALIZER,\n  platformCore,\n  PlatformRef,\n  Provider,\n  RendererFactory2,\n  StaticProvider,\n  Testability,\n  TestabilityRegistry,\n  Type,\n  ɵINJECTOR_SCOPE as INJECTOR_SCOPE,\n  ɵinternalCreateApplication as internalCreateApplication,\n  ɵRuntimeError as RuntimeError,\n  ɵsetDocument,\n  ɵTESTABILITY as TESTABILITY,\n  ɵTESTABILITY_GETTER as TESTABILITY_GETTER,\n  inject,\n} from '@angular/core';\n\nimport {BrowserDomAdapter} from './browser/browser_adapter';\nimport {BrowserGetTestability} from './browser/testability';\nimport {BrowserXhr} from './browser/xhr';\nimport {DomRendererFactory2} from './dom/dom_renderer';\nimport {DomEventsPlugin} from './dom/events/dom_events';\nimport {EVENT_MANAGER_PLUGINS, EventManager} from './dom/events/event_manager';\nimport {KeyEventsPlugin} from './dom/events/key_events';\nimport {SharedStylesHost} from './dom/shared_styles_host';\nimport {RuntimeErrorCode} from './errors';\n\n/**\n * Set of config options available during the application bootstrap operation.\n *\n * @publicApi\n *\n * @deprecated\n * `ApplicationConfig` has moved, please import `ApplicationConfig` from `@angular/core` instead.\n */\n// The below is a workaround to add a deprecated message.\ntype ApplicationConfig = ApplicationConfigFromCore;\nexport {ApplicationConfig};\n\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/components/importing).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```angular-ts\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```ts\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```ts\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```ts\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nexport function bootstrapApplication(\n  rootComponent: Type<unknown>,\n  options?: ApplicationConfig,\n): Promise<ApplicationRef> {\n  return internalCreateApplication({rootComponent, ...createProvidersConfig(options)});\n}\n\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nexport function createApplication(options?: ApplicationConfig) {\n  return internalCreateApplication(createProvidersConfig(options));\n}\n\nfunction createProvidersConfig(options?: ApplicationConfig) {\n  return {\n    appProviders: [...BROWSER_MODULE_PROVIDERS, ...(options?.providers ?? [])],\n    platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS,\n  };\n}\n\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @publicApi\n */\nexport function provideProtractorTestingSupport(): Provider[] {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideProtractorTestingSupport` call results in app\n  // code.\n  return [...TESTABILITY_PROVIDERS];\n}\n\nexport function initDomAdapter() {\n  BrowserDomAdapter.makeCurrent();\n}\n\nexport function errorHandler(): ErrorHandler {\n  return new ErrorHandler();\n}\n\nexport function _document(): any {\n  // Tell ivy about the global document\n  ɵsetDocument(document);\n  return document;\n}\n\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS: StaticProvider[] = [\n  {provide: PLATFORM_ID, useValue: PLATFORM_BROWSER_ID},\n  {provide: PLATFORM_INITIALIZER, useValue: initDomAdapter, multi: true},\n  {provide: DOCUMENT, useFactory: _document},\n];\n\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nexport const platformBrowser: (extraProviders?: StaticProvider[]) => PlatformRef =\n  createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(\n  typeof ngDevMode === 'undefined' || ngDevMode ? 'BrowserModule Providers Marker' : '',\n);\n\nconst TESTABILITY_PROVIDERS = [\n  {\n    provide: TESTABILITY_GETTER,\n    useClass: BrowserGetTestability,\n  },\n  {\n    provide: TESTABILITY,\n    useClass: Testability,\n    deps: [NgZone, TestabilityRegistry, TESTABILITY_GETTER],\n  },\n  {\n    provide: Testability, // Also provide as `Testability` for backwards-compatibility.\n    useClass: Testability,\n    deps: [NgZone, TestabilityRegistry, TESTABILITY_GETTER],\n  },\n];\n\nconst BROWSER_MODULE_PROVIDERS: Provider[] = [\n  {provide: INJECTOR_SCOPE, useValue: 'root'},\n  {provide: ErrorHandler, useFactory: errorHandler},\n  {\n    provide: EVENT_MANAGER_PLUGINS,\n    useClass: DomEventsPlugin,\n    multi: true,\n    deps: [DOCUMENT],\n  },\n  {provide: EVENT_MANAGER_PLUGINS, useClass: KeyEventsPlugin, multi: true, deps: [DOCUMENT]},\n  DomRendererFactory2,\n  SharedStylesHost,\n  EventManager,\n  {provide: RendererFactory2, useExisting: DomRendererFactory2},\n  {provide: XhrFactory, useClass: BrowserXhr},\n  typeof ngDevMode === 'undefined' || ngDevMode\n    ? {provide: BROWSER_MODULE_PROVIDERS_MARKER, useValue: true}\n    : [],\n];\n\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\n@NgModule({\n  providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n  exports: [CommonModule, ApplicationModule],\n})\nexport class BrowserModule {\n  constructor() {\n    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n      const providersAlreadyPresent = inject(BROWSER_MODULE_PROVIDERS_MARKER, {\n        optional: true,\n        skipSelf: true,\n      });\n\n      if (providersAlreadyPresent) {\n        throw new RuntimeError(\n          RuntimeErrorCode.BROWSER_MODULE_ALREADY_LOADED,\n          `Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` +\n            `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`,\n        );\n      }\n    }\n  }\n}\n"], "names": ["DomAdapter", "setRootDomAdapter", "parseCookieValue", "global", "RuntimeError", "getDOM", "internalCreateApplication", "ɵsetDocument", "PLATFORM_BROWSER_ID", "TESTABILITY_GETTER", "TESTABILITY", "INJECTOR_SCOPE"], "mappings": ";;;;;;;;;;;AAcA;;;;;AAKG;AACG,MAAO,iBAAkB,SAAQA,WAAU,CAAA;IAC7B,iBAAiB,GAAY,IAAI;AAEnD,IAAA,OAAO,WAAW,GAAA;AAChB,QAAAC,kBAAiB,CAAC,IAAI,iBAAiB,EAAE,CAAC;;AAGnC,IAAA,WAAW,CAAC,EAAQ,EAAE,GAAQ,EAAE,QAAa,EAAE,OAAY,EAAA;QAClE,EAAE,CAAC,gBAAgB,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC;AAC3C,QAAA,OAAO,MAAK;YACV,EAAE,CAAC,mBAAmB,CAAC,GAAG,EAAE,QAAQ,EAAE,OAAO,CAAC;AAChD,SAAC;;IAEM,aAAa,CAAC,EAAQ,EAAE,GAAQ,EAAA;AACvC,QAAA,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC;;AAEd,IAAA,MAAM,CAAC,IAAU,EAAA;QACvB,IAAiC,CAAC,MAAM,EAAE;;IAEpC,aAAa,CAAC,OAAe,EAAE,GAAc,EAAA;AACpD,QAAA,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,kBAAkB,EAAE;AACtC,QAAA,OAAO,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC;;IAE1B,kBAAkB,GAAA;QACzB,OAAO,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAC,WAAW,CAAC;;IAEvD,kBAAkB,GAAA;AACzB,QAAA,OAAO,QAAQ;;AAGR,IAAA,aAAa,CAAC,IAAU,EAAA;AAC/B,QAAA,OAAO,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY;;AAGnC,IAAA,YAAY,CAAC,IAAS,EAAA;QAC7B,OAAO,IAAI,YAAY,gBAAgB;;;IAIhC,oBAAoB,CAAC,GAAa,EAAE,MAAc,EAAA;AACzD,QAAA,IAAI,MAAM,KAAK,QAAQ,EAAE;AACvB,YAAA,OAAO,MAAM;;AAEf,QAAA,IAAI,MAAM,KAAK,UAAU,EAAE;AACzB,YAAA,OAAO,GAAG;;AAEZ,QAAA,IAAI,MAAM,KAAK,MAAM,EAAE;YACrB,OAAO,GAAG,CAAC,IAAI;;AAEjB,QAAA,OAAO,IAAI;;AAEJ,IAAA,WAAW,CAAC,GAAa,EAAA;AAChC,QAAA,MAAM,IAAI,GAAG,kBAAkB,EAAE;AACjC,QAAA,OAAO,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;;IAExC,gBAAgB,GAAA;QACvB,WAAW,GAAG,IAAI;;IAEX,YAAY,GAAA;AACnB,QAAA,OAAO,MAAM,CAAC,SAAS,CAAC,SAAS;;AAE1B,IAAA,SAAS,CAAC,IAAY,EAAA;QAC7B,OAAOC,iBAAgB,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC;;AAEjD;AAED,IAAI,WAAW,GAAuB,IAAI;AAC1C,SAAS,kBAAkB,GAAA;IACzB,WAAW,GAAG,WAAW,IAAI,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;AAChE,IAAA,OAAO,WAAW,GAAG,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI;AAC9D;AAEA,SAAS,YAAY,CAAC,GAAW,EAAA;;;IAG/B,OAAO,IAAI,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAQ;AAChD;;MC7Ea,qBAAqB,CAAA;AAChC,IAAA,WAAW,CAAC,QAA6B,EAAA;QACvCC,OAAM,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAS,EAAE,eAAA,GAA2B,IAAI,KAAI;YAC/E,MAAM,WAAW,GAAG,QAAQ,CAAC,qBAAqB,CAAC,IAAI,EAAE,eAAe,CAAC;AACzE,YAAA,IAAI,WAAW,IAAI,IAAI,EAAE;gBACvB,MAAM,IAAIC,aAAY,CAAA,IAAA,+CAEpB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC5C,oBAAA,yCAAyC,CAC5C;;AAEH,YAAA,OAAO,WAAW;AACpB,SAAC;QAEDD,OAAM,CAAC,4BAA4B,CAAC,GAAG,MAAM,QAAQ,CAAC,mBAAmB,EAAE;QAE3EA,OAAM,CAAC,2BAA2B,CAAC,GAAG,MAAM,QAAQ,CAAC,kBAAkB,EAAE;AAEzE,QAAA,MAAM,aAAa,GAAG,CAAC,QAAoB,KAAI;AAC7C,YAAA,MAAM,aAAa,GAAGA,OAAM,CAAC,4BAA4B,CAAC,EAAmB;AAC7E,YAAA,IAAI,KAAK,GAAG,aAAa,CAAC,MAAM;AAChC,YAAA,MAAM,SAAS,GAAG,YAAA;AAChB,gBAAA,KAAK,EAAE;AACP,gBAAA,IAAI,KAAK,IAAI,CAAC,EAAE;AACd,oBAAA,QAAQ,EAAE;;AAEd,aAAC;AACD,YAAA,aAAa,CAAC,OAAO,CAAC,CAAC,WAAW,KAAI;AACpC,gBAAA,WAAW,CAAC,UAAU,CAAC,SAAS,CAAC;AACnC,aAAC,CAAC;AACJ,SAAC;AAED,QAAA,IAAI,CAACA,OAAM,CAAC,sBAAsB,CAAC,EAAE;AACnC,YAAAA,OAAM,CAAC,sBAAsB,CAAC,GAAG,EAAE;;QAErCA,OAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC;;AAGpD,IAAA,qBAAqB,CACnB,QAA6B,EAC7B,IAAS,EACT,eAAwB,EAAA;AAExB,QAAA,IAAI,IAAI,IAAI,IAAI,EAAE;AAChB,YAAA,OAAO,IAAI;;QAEb,MAAM,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC;AACvC,QAAA,IAAI,CAAC,IAAI,IAAI,EAAE;AACb,YAAA,OAAO,CAAC;;aACH,IAAI,CAAC,eAAe,EAAE;AAC3B,YAAA,OAAO,IAAI;;QAEb,IAAIE,OAAM,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;AAC/B,YAAA,OAAO,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAQ,IAAK,CAAC,IAAI,EAAE,IAAI,CAAC;;AAErE,QAAA,OAAO,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC;;AAExE;;ACjED;;AAEG;MAEU,UAAU,CAAA;IACrB,KAAK,GAAA;QACH,OAAO,IAAI,cAAc,EAAE;;kHAFlB,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAAV,UAAU,EAAA,CAAA;;sGAAV,UAAU,EAAA,UAAA,EAAA,CAAA;kBADtB;;;ACAK,MAAO,eAAgB,SAAQ,kBAAkB,CAAA;AACrD,IAAA,WAAA,CAA8B,GAAQ,EAAA;QACpC,KAAK,CAAC,GAAG,CAAC;;;;AAKH,IAAA,QAAQ,CAAC,SAAiB,EAAA;AACjC,QAAA,OAAO,IAAI;;AAGJ,IAAA,gBAAgB,CACvB,OAAoB,EACpB,SAAiB,EACjB,OAAiB,EACjB,OAAyB,EAAA;QAEzB,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAwB,EAAE,OAAO,CAAC;AACtE,QAAA,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,SAAS,EAAE,OAAwB,EAAE,OAAO,CAAC;;AAG9F,IAAA,mBAAmB,CACjB,MAAW,EACX,SAAiB,EACjB,QAAkB,EAClB,OAAyB,EAAA;QAEzB,OAAO,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,QAAyB,EAAE,OAAO,CAAC;;AA3BvE,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,kBACN,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHADjB,eAAe,EAAA,CAAA;;sGAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B;;0BAEc,MAAM;2BAAC,QAAQ;;;ACF9B;;AAEG;AACH,MAAM,aAAa,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;AAEzD;AACA;AACA,MAAM,OAAO,GAA0B;AACrC,IAAA,IAAI,EAAE,WAAW;AACjB,IAAA,IAAI,EAAE,KAAK;AACX,IAAA,MAAM,EAAE,QAAQ;AAChB,IAAA,MAAM,EAAE,QAAQ;AAChB,IAAA,KAAK,EAAE,QAAQ;AACf,IAAA,KAAK,EAAE,QAAQ;AACf,IAAA,MAAM,EAAE,WAAW;AACnB,IAAA,OAAO,EAAE,YAAY;AACrB,IAAA,IAAI,EAAE,SAAS;AACf,IAAA,MAAM,EAAE,WAAW;AACnB,IAAA,MAAM,EAAE,aAAa;AACrB,IAAA,QAAQ,EAAE,YAAY;AACtB,IAAA,KAAK,EAAE,IAAI;CACZ;AAED;;AAEG;AACH,MAAM,oBAAoB,GAAuD;IAC/E,KAAK,EAAE,CAAC,KAAoB,KAAK,KAAK,CAAC,MAAM;IAC7C,SAAS,EAAE,CAAC,KAAoB,KAAK,KAAK,CAAC,OAAO;IAClD,MAAM,EAAE,CAAC,KAAoB,KAAK,KAAK,CAAC,OAAO;IAC/C,OAAO,EAAE,CAAC,KAAoB,KAAK,KAAK,CAAC,QAAQ;CAClD;AAED;;AAEG;AAEG,MAAO,eAAgB,SAAQ,kBAAkB,CAAA;AACrD;;;AAGG;AACH,IAAA,WAAA,CAA8B,GAAQ,EAAA;QACpC,KAAK,CAAC,GAAG,CAAC;;AAGZ;;;;AAIG;AACM,IAAA,QAAQ,CAAC,SAAiB,EAAA;QACjC,OAAO,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,IAAI;;AAG1D;;;;;;;AAOG;AACM,IAAA,gBAAgB,CACvB,OAAoB,EACpB,SAAiB,EACjB,OAAiB,EACjB,OAAyB,EAAA;QAEzB,MAAM,WAAW,GAAG,eAAe,CAAC,cAAc,CAAC,SAAS,CAAE;QAE9D,MAAM,cAAc,GAAG,eAAe,CAAC,aAAa,CAClD,WAAW,CAAC,SAAS,CAAC,EACtB,OAAO,EACP,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CACvB;QAED,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,iBAAiB,CAAC,MAAK;AACnD,YAAA,OAAOA,OAAM,EAAE,CAAC,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,cAAc,CAAC,EAAE,cAAc,EAAE,OAAO,CAAC;AAC5F,SAAC,CAAC;;AAGJ;;;;;;;;AAQG;IACH,OAAO,cAAc,CAAC,SAAiB,EAAA;QACrC,MAAM,KAAK,GAAa,SAAS,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC;AAE1D,QAAA,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE;AAClC,QAAA,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,YAAY,KAAK,SAAS,IAAI,YAAY,KAAK,OAAO,CAAC,EAAE;AACnF,YAAA,OAAO,IAAI;;QAGb,MAAM,GAAG,GAAG,eAAe,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,EAAG,CAAC;QAEvD,IAAI,OAAO,GAAG,EAAE;QAChB,IAAI,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC;AAClC,QAAA,IAAI,MAAM,GAAG,CAAC,CAAC,EAAE;AACf,YAAA,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;YACvB,OAAO,GAAG,OAAO;;AAEnB,QAAA,aAAa,CAAC,OAAO,CAAC,CAAC,YAAY,KAAI;YACrC,MAAM,KAAK,GAAW,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC;AACjD,YAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACd,gBAAA,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AACtB,gBAAA,OAAO,IAAI,YAAY,GAAG,GAAG;;AAEjC,SAAC,CAAC;QACF,OAAO,IAAI,GAAG;AAEd,QAAA,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;;AAEzC,YAAA,OAAO,IAAI;;;;;QAMb,MAAM,MAAM,GAA4C,EAAS;AACjE,QAAA,MAAM,CAAC,cAAc,CAAC,GAAG,YAAY;AACrC,QAAA,MAAM,CAAC,SAAS,CAAC,GAAG,OAAO;AAC3B,QAAA,OAAO,MAAM;;AAGf;;;;;;;;;AASG;AACH,IAAA,OAAO,qBAAqB,CAAC,KAAoB,EAAE,WAAmB,EAAA;AACpE,QAAA,IAAI,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG;QAC7C,IAAI,GAAG,GAAG,EAAE;QACZ,IAAI,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;AACrC,YAAA,OAAO,GAAG,KAAK,CAAC,IAAI;YACpB,GAAG,GAAG,OAAO;;;AAGf,QAAA,IAAI,OAAO,IAAI,IAAI,IAAI,CAAC,OAAO;AAAE,YAAA,OAAO,KAAK;AAC7C,QAAA,OAAO,GAAG,OAAO,CAAC,WAAW,EAAE;AAC/B,QAAA,IAAI,OAAO,KAAK,GAAG,EAAE;AACnB,YAAA,OAAO,GAAG,OAAO,CAAC;;AACb,aAAA,IAAI,OAAO,KAAK,GAAG,EAAE;AAC1B,YAAA,OAAO,GAAG,KAAK,CAAC;;AAElB,QAAA,aAAa,CAAC,OAAO,CAAC,CAAC,YAAY,KAAI;AACrC,YAAA,IAAI,YAAY,KAAK,OAAO,EAAE;AAC5B,gBAAA,MAAM,cAAc,GAAG,oBAAoB,CAAC,YAAY,CAAC;AACzD,gBAAA,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;AACzB,oBAAA,GAAG,IAAI,YAAY,GAAG,GAAG;;;AAG/B,SAAC,CAAC;QACF,GAAG,IAAI,OAAO;QACd,OAAO,GAAG,KAAK,WAAW;;AAG5B;;;;;;AAMG;AACH,IAAA,OAAO,aAAa,CAAC,OAAe,EAAE,OAAiB,EAAE,IAAY,EAAA;QACnE,OAAO,CAAC,KAAoB,KAAI;YAC9B,IAAI,eAAe,CAAC,qBAAqB,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE;gBACzD,IAAI,CAAC,UAAU,CAAC,MAAM,OAAO,CAAC,KAAK,CAAC,CAAC;;AAEzC,SAAC;;;IAIH,OAAO,aAAa,CAAC,OAAe,EAAA;QAClC,OAAO,OAAO,KAAK,KAAK,GAAG,QAAQ,GAAG,OAAO;;AAnJpC,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,kBAKN,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHALjB,eAAe,EAAA,CAAA;;sGAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B;;0BAMc,MAAM;2BAAC,QAAQ;;;ACS9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0DG;AACa,SAAA,oBAAoB,CAClC,aAA4B,EAC5B,OAA2B,EAAA;AAE3B,IAAA,OAAOC,0BAAyB,CAAC,EAAC,aAAa,EAAE,GAAG,qBAAqB,CAAC,OAAO,CAAC,EAAC,CAAC;AACtF;AAEA;;;;;;;;;;;AAWG;AACG,SAAU,iBAAiB,CAAC,OAA2B,EAAA;AAC3D,IAAA,OAAOA,0BAAyB,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC;AAClE;AAEA,SAAS,qBAAqB,CAAC,OAA2B,EAAA;IACxD,OAAO;AACL,QAAA,YAAY,EAAE,CAAC,GAAG,wBAAwB,EAAE,IAAI,OAAO,EAAE,SAAS,IAAI,EAAE,CAAC,CAAC;AAC1E,QAAA,iBAAiB,EAAE,mCAAmC;KACvD;AACH;AAEA;;;;;;;;;;AAUG;SACa,+BAA+B,GAAA;;;;AAI7C,IAAA,OAAO,CAAC,GAAG,qBAAqB,CAAC;AACnC;SAEgB,cAAc,GAAA;IAC5B,iBAAiB,CAAC,WAAW,EAAE;AACjC;SAEgB,YAAY,GAAA;IAC1B,OAAO,IAAI,YAAY,EAAE;AAC3B;SAEgB,SAAS,GAAA;;IAEvBC,YAAY,CAAC,QAAQ,CAAC;AACtB,IAAA,OAAO,QAAQ;AACjB;AAEA,MAAM,mCAAmC,GAAqB;AAC5D,IAAA,EAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAEC,oBAAmB,EAAC;IACrD,EAAC,OAAO,EAAE,oBAAoB,EAAE,QAAQ,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,EAAC;AACtE,IAAA,EAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAC;CAC3C;AAED;;;;;AAKG;AACI,MAAM,eAAe,GAC1B,qBAAqB,CAAC,YAAY,EAAE,SAAS,EAAE,mCAAmC;AAEpF;;;;;AAKG;AACH,MAAM,+BAA+B,GAAG,IAAI,cAAc,CACxD,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,gCAAgC,GAAG,EAAE,CACtF;AAED,MAAM,qBAAqB,GAAG;AAC5B,IAAA;AACE,QAAA,OAAO,EAAEC,mBAAkB;AAC3B,QAAA,QAAQ,EAAE,qBAAqB;AAChC,KAAA;AACD,IAAA;AACE,QAAA,OAAO,EAAEC,YAAW;AACpB,QAAA,QAAQ,EAAE,WAAW;AACrB,QAAA,IAAI,EAAE,CAAC,MAAM,EAAE,mBAAmB,EAAED,mBAAkB,CAAC;AACxD,KAAA;AACD,IAAA;QACE,OAAO,EAAE,WAAW;AACpB,QAAA,QAAQ,EAAE,WAAW;AACrB,QAAA,IAAI,EAAE,CAAC,MAAM,EAAE,mBAAmB,EAAEA,mBAAkB,CAAC;AACxD,KAAA;CACF;AAED,MAAM,wBAAwB,GAAe;AAC3C,IAAA,EAAC,OAAO,EAAEE,eAAc,EAAE,QAAQ,EAAE,MAAM,EAAC;AAC3C,IAAA,EAAC,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAC;AACjD,IAAA;AACE,QAAA,OAAO,EAAE,qBAAqB;AAC9B,QAAA,QAAQ,EAAE,eAAe;AACzB,QAAA,KAAK,EAAE,IAAI;QACX,IAAI,EAAE,CAAC,QAAQ,CAAC;AACjB,KAAA;AACD,IAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,EAAE,eAAe,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,EAAC;IAC1F,mBAAmB;IACnB,gBAAgB;IAChB,YAAY;AACZ,IAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,mBAAmB,EAAC;AAC7D,IAAA,EAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAC;AAC3C,IAAA,OAAO,SAAS,KAAK,WAAW,IAAI;UAChC,EAAC,OAAO,EAAE,+BAA+B,EAAE,QAAQ,EAAE,IAAI;AAC3D,UAAE,EAAE;CACP;AAED;;;;;;;;AAQG;MAKU,aAAa,CAAA;AACxB,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,YAAA,MAAM,uBAAuB,GAAG,MAAM,CAAC,+BAA+B,EAAE;AACtE,gBAAA,QAAQ,EAAE,IAAI;AACd,gBAAA,QAAQ,EAAE,IAAI;AACf,aAAA,CAAC;YAEF,IAAI,uBAAuB,EAAE;gBAC3B,MAAM,IAAIP,aAAY,CAAA,IAAA,uDAEpB,CAAoF,kFAAA,CAAA;AAClF,oBAAA,CAAA,iFAAA,CAAmF,CACtF;;;;kHAbI,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;mHAAb,aAAa,EAAA,OAAA,EAAA,CAFd,YAAY,EAAE,iBAAiB,CAAA,EAAA,CAAA;mHAE9B,aAAa,EAAA,SAAA,EAHb,CAAC,GAAG,wBAAwB,EAAE,GAAG,qBAAqB,CAAC,EAAA,OAAA,EAAA,CACxD,YAAY,EAAE,iBAAiB,CAAA,EAAA,CAAA;;sGAE9B,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,SAAS,EAAE,CAAC,GAAG,wBAAwB,EAAE,GAAG,qBAAqB,CAAC;AAClE,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,iBAAiB,CAAC;AAC3C,iBAAA;;;;;"}