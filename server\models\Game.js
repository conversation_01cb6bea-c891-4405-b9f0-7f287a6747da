const mongoose = require('mongoose');

const playerSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    minlength: 2,
    maxlength: 20
  },
  is_leader: {
    type: Boolean,
    default: false
  },
  score: {
    type: Number,
    default: 0
  },
  joined_at: {
    type: Date,
    default: Date.now
  },
  last_seen: {
    type: Date,
    default: Date.now
  },
  connection_id: {
    type: String,
    default: null
  }
});

const roundSchema = new mongoose.Schema({
  round_number: {
    type: Number,
    required: true
  },
  winner: {
    type: String,
    default: null
  },
  winning_content: {
    type: String,
    default: null
  },
  completed_at: {
    type: Date,
    default: Date.now
  }
});

const gameSchema = new mongoose.Schema({
  room_code: {
    type: String,
    required: true,
    unique: true,
    length: 4,
    uppercase: true,
    match: /^[A-Z]{4}$/
  },
  game_type: {
    type: String,
    required: true,
    enum: ['poisoning_pigeons', 'florida_man', 'washington_path', 'pick_your_nose']
  },
  game_status: {
    type: String,
    required: true,
    default: 'Waiting for Players'
  },
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  },
  
  // Game Configuration
  settings: {
    max_players: {
      type: Number,
      default: 8,
      min: 2,
      max: 12
    },
    rounds_to_win: {
      type: Number,
      default: 5,
      min: 1,
      max: 10
    },
    round_time_limit: {
      type: Number,
      default: 30,
      min: 10,
      max: 120
    },
    mature_content: {
      type: Boolean,
      default: false
    }
  },
  
  // Current Game State
  current_round: {
    type: Number,
    default: 0
  },
  current_phase: {
    type: String,
    default: 'waiting'
  },
  current_judge: {
    type: String,
    default: null
  },
  
  // Players
  players: [playerSchema],
  
  // Game-Specific Data (flexible structure)
  game_data: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  
  // Round History
  rounds: [roundSchema]
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
});

// Indexes
gameSchema.index({ room_code: 1 });
gameSchema.index({ created_at: 1 });
gameSchema.index({ updated_at: 1 });

// Methods
gameSchema.methods.addPlayer = function(playerData) {
  // Check if player already exists
  const existingPlayer = this.players.find(p => p.name === playerData.name);
  if (existingPlayer) {
    // Update existing player
    existingPlayer.last_seen = new Date();
    existingPlayer.connection_id = playerData.connection_id;
    return existingPlayer;
  }
  
  // Add new player
  const newPlayer = {
    name: playerData.name,
    is_leader: this.players.length === 0, // First player is leader
    connection_id: playerData.connection_id
  };
  
  this.players.push(newPlayer);
  return newPlayer;
};

gameSchema.methods.removePlayer = function(playerName) {
  const playerIndex = this.players.findIndex(p => p.name === playerName);
  if (playerIndex === -1) return null;
  
  const removedPlayer = this.players[playerIndex];
  this.players.splice(playerIndex, 1);
  
  // If leader left, assign new leader
  if (removedPlayer.is_leader && this.players.length > 0) {
    this.players[0].is_leader = true;
  }
  
  return removedPlayer;
};

gameSchema.methods.getPlayer = function(playerName) {
  return this.players.find(p => p.name === playerName);
};

gameSchema.methods.updatePlayerScore = function(playerName, points) {
  const player = this.getPlayer(playerName);
  if (player) {
    player.score += points;
    return player.score;
  }
  return null;
};

gameSchema.methods.getLeader = function() {
  return this.players.find(p => p.is_leader);
};

gameSchema.methods.isGameComplete = function() {
  return this.players.some(p => p.score >= this.settings.rounds_to_win);
};

gameSchema.methods.getWinner = function() {
  return this.players.find(p => p.score >= this.settings.rounds_to_win);
};

// Static methods
gameSchema.statics.generateRoomCode = function() {
  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let code = '';
  for (let i = 0; i < 4; i++) {
    code += letters.charAt(Math.floor(Math.random() * letters.length));
  }
  return code;
};

gameSchema.statics.findByRoomCode = function(roomCode) {
  return this.findOne({ room_code: roomCode.toUpperCase() });
};

// Pre-save middleware
gameSchema.pre('save', function(next) {
  this.updated_at = new Date();
  next();
});

module.exports = mongoose.model('Game', gameSchema);
