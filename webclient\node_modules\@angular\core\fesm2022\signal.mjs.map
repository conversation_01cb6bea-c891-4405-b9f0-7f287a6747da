{"version": 3, "file": "signal.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/primitives/signals/src/equality.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/primitives/signals/src/graph.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/primitives/signals/src/computed.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/primitives/signals/src/errors.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/core/primitives/signals/src/signal.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * A comparison function which can determine if two values are equal.\n */\nexport type ValueEqualityFn<T> = (a: T, b: T) => boolean;\n\n/**\n * The default equality function used for `signal` and `computed`, which uses referential equality.\n */\nexport function defaultEquals<T>(a: T, b: T) {\n  return Object.is(a, b);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// Required as the signals library is in a separate package, so we need to explicitly ensure the\n// global `ngDevMode` type is defined.\ndeclare const ngDevMode: boolean | undefined;\n\n/**\n * The currently active consumer `ReactiveNode`, if running code in a reactive context.\n *\n * Change this via `setActiveConsumer`.\n */\nlet activeConsumer: ReactiveNode | null = null;\nlet inNotificationPhase = false;\n\ntype Version = number & {__brand: 'Version'};\n\n/**\n * Global epoch counter. Incremented whenever a source signal is set.\n */\nlet epoch: Version = 1 as Version;\n\nexport type ReactiveHookFn = (node: ReactiveNode) => void;\n\n/**\n * If set, called after a producer `ReactiveNode` is created.\n */\nlet postProducerCreatedFn: ReactiveHookFn | null = null;\n\n/**\n * Symbol used to tell `Signal`s apart from other functions.\n *\n * This can be used to auto-unwrap signals in various cases, or to auto-wrap non-signal values.\n */\nexport const SIGNAL: unique symbol = /* @__PURE__ */ Symbol('SIGNAL');\n\nexport function setActiveConsumer(consumer: ReactiveNode | null): ReactiveNode | null {\n  const prev = activeConsumer;\n  activeConsumer = consumer;\n  return prev;\n}\n\nexport function getActiveConsumer(): ReactiveNode | null {\n  return activeConsumer;\n}\n\nexport function isInNotificationPhase(): boolean {\n  return inNotificationPhase;\n}\n\nexport interface Reactive {\n  [SIGNAL]: ReactiveNode;\n}\n\nexport function isReactive(value: unknown): value is Reactive {\n  return (value as Partial<Reactive>)[SIGNAL] !== undefined;\n}\n\nexport const REACTIVE_NODE: ReactiveNode = {\n  version: 0 as Version,\n  lastCleanEpoch: 0 as Version,\n  dirty: false,\n  producerNode: undefined,\n  producerLastReadVersion: undefined,\n  producerIndexOfThis: undefined,\n  nextProducerIndex: 0,\n  liveConsumerNode: undefined,\n  liveConsumerIndexOfThis: undefined,\n  consumerAllowSignalWrites: false,\n  consumerIsAlwaysLive: false,\n  kind: 'unknown',\n  producerMustRecompute: () => false,\n  producerRecomputeValue: () => {},\n  consumerMarkedDirty: () => {},\n  consumerOnSignalRead: () => {},\n};\n\n/**\n * A producer and/or consumer which participates in the reactive graph.\n *\n * Producer `ReactiveNode`s which are accessed when a consumer `ReactiveNode` is the\n * `activeConsumer` are tracked as dependencies of that consumer.\n *\n * Certain consumers are also tracked as \"live\" consumers and create edges in the other direction,\n * from producer to consumer. These edges are used to propagate change notifications when a\n * producer's value is updated.\n *\n * A `ReactiveNode` may be both a producer and consumer.\n */\nexport interface ReactiveNode {\n  /**\n   * Version of the value that this node produces.\n   *\n   * This is incremented whenever a new value is produced by this node which is not equal to the\n   * previous value (by whatever definition of equality is in use).\n   */\n  version: Version;\n\n  /**\n   * Epoch at which this node is verified to be clean.\n   *\n   * This allows skipping of some polling operations in the case where no signals have been set\n   * since this node was last read.\n   */\n  lastCleanEpoch: Version;\n\n  /**\n   * Whether this node (in its consumer capacity) is dirty.\n   *\n   * Only live consumers become dirty, when receiving a change notification from a dependency\n   * producer.\n   */\n  dirty: boolean;\n\n  /**\n   * Producers which are dependencies of this consumer.\n   *\n   * Uses the same indices as the `producerLastReadVersion` and `producerIndexOfThis` arrays.\n   */\n  producerNode: ReactiveNode[] | undefined;\n\n  /**\n   * `Version` of the value last read by a given producer.\n   *\n   * Uses the same indices as the `producerNode` and `producerIndexOfThis` arrays.\n   */\n  producerLastReadVersion: Version[] | undefined;\n\n  /**\n   * Index of `this` (consumer) in each producer's `liveConsumers` array.\n   *\n   * This value is only meaningful if this node is live (`liveConsumers.length > 0`). Otherwise\n   * these indices are stale.\n   *\n   * Uses the same indices as the `producerNode` and `producerLastReadVersion` arrays.\n   */\n  producerIndexOfThis: number[] | undefined;\n\n  /**\n   * Index into the producer arrays that the next dependency of this node as a consumer will use.\n   *\n   * This index is zeroed before this node as a consumer begins executing. When a producer is read,\n   * it gets inserted into the producers arrays at this index. There may be an existing dependency\n   * in this location which may or may not match the incoming producer, depending on whether the\n   * same producers were read in the same order as the last computation.\n   */\n  nextProducerIndex: number;\n\n  /**\n   * Array of consumers of this producer that are \"live\" (they require push notifications).\n   *\n   * `liveConsumerNode.length` is effectively our reference count for this node.\n   */\n  liveConsumerNode: ReactiveNode[] | undefined;\n\n  /**\n   * Index of `this` (producer) in each consumer's `producerNode` array.\n   *\n   * Uses the same indices as the `liveConsumerNode` array.\n   */\n  liveConsumerIndexOfThis: number[] | undefined;\n\n  /**\n   * Whether writes to signals are allowed when this consumer is the `activeConsumer`.\n   *\n   * This is used to enforce guardrails such as preventing writes to writable signals in the\n   * computation function of computed signals, which is supposed to be pure.\n   */\n  consumerAllowSignalWrites: boolean;\n\n  readonly consumerIsAlwaysLive: boolean;\n\n  /**\n   * Tracks whether producers need to recompute their value independently of the reactive graph (for\n   * example, if no initial value has been computed).\n   */\n  producerMustRecompute(node: unknown): boolean;\n  producerRecomputeValue(node: unknown): void;\n  consumerMarkedDirty(node: unknown): void;\n\n  /**\n   * Called when a signal is read within this consumer.\n   */\n  consumerOnSignalRead(node: unknown): void;\n\n  /**\n   * A debug name for the reactive node. Used in Angular DevTools to identify the node.\n   */\n  debugName?: string;\n\n  /**\n   * Kind of node. Example: 'signal', 'computed', 'input', 'effect'.\n   *\n   * ReactiveNode has this as 'unknown' by default, but derived node types should override this to\n   * make available the kind of signal that particular instance of a ReactiveNode represents.\n   *\n   * Used in Angular DevTools to identify the kind of signal.\n   */\n  kind: string;\n}\n\ninterface ConsumerNode extends ReactiveNode {\n  producerNode: NonNullable<ReactiveNode['producerNode']>;\n  producerIndexOfThis: NonNullable<ReactiveNode['producerIndexOfThis']>;\n  producerLastReadVersion: NonNullable<ReactiveNode['producerLastReadVersion']>;\n}\n\ninterface ProducerNode extends ReactiveNode {\n  liveConsumerNode: NonNullable<ReactiveNode['liveConsumerNode']>;\n  liveConsumerIndexOfThis: NonNullable<ReactiveNode['liveConsumerIndexOfThis']>;\n}\n\n/**\n * Called by implementations when a producer's signal is read.\n */\nexport function producerAccessed(node: ReactiveNode): void {\n  if (inNotificationPhase) {\n    throw new Error(\n      typeof ngDevMode !== 'undefined' && ngDevMode\n        ? `Assertion error: signal read during notification phase`\n        : '',\n    );\n  }\n\n  if (activeConsumer === null) {\n    // Accessed outside of a reactive context, so nothing to record.\n    return;\n  }\n\n  activeConsumer.consumerOnSignalRead(node);\n\n  // This producer is the `idx`th dependency of `activeConsumer`.\n  const idx = activeConsumer.nextProducerIndex++;\n\n  assertConsumerNode(activeConsumer);\n\n  if (idx < activeConsumer.producerNode.length && activeConsumer.producerNode[idx] !== node) {\n    // There's been a change in producers since the last execution of `activeConsumer`.\n    // `activeConsumer.producerNode[idx]` holds a stale dependency which will be be removed and\n    // replaced with `this`.\n    //\n    // If `activeConsumer` isn't live, then this is a no-op, since we can replace the producer in\n    // `activeConsumer.producerNode` directly. However, if `activeConsumer` is live, then we need\n    // to remove it from the stale producer's `liveConsumer`s.\n    if (consumerIsLive(activeConsumer)) {\n      const staleProducer = activeConsumer.producerNode[idx];\n      producerRemoveLiveConsumerAtIndex(staleProducer, activeConsumer.producerIndexOfThis[idx]);\n\n      // At this point, the only record of `staleProducer` is the reference at\n      // `activeConsumer.producerNode[idx]` which will be overwritten below.\n    }\n  }\n\n  if (activeConsumer.producerNode[idx] !== node) {\n    // We're a new dependency of the consumer (at `idx`).\n    activeConsumer.producerNode[idx] = node;\n\n    // If the active consumer is live, then add it as a live consumer. If not, then use 0 as a\n    // placeholder value.\n    activeConsumer.producerIndexOfThis[idx] = consumerIsLive(activeConsumer)\n      ? producerAddLiveConsumer(node, activeConsumer, idx)\n      : 0;\n  }\n  activeConsumer.producerLastReadVersion[idx] = node.version;\n}\n\n/**\n * Increment the global epoch counter.\n *\n * Called by source producers (that is, not computeds) whenever their values change.\n */\nexport function producerIncrementEpoch(): void {\n  epoch++;\n}\n\n/**\n * Ensure this producer's `version` is up-to-date.\n */\nexport function producerUpdateValueVersion(node: ReactiveNode): void {\n  if (consumerIsLive(node) && !node.dirty) {\n    // A live consumer will be marked dirty by producers, so a clean state means that its version\n    // is guaranteed to be up-to-date.\n    return;\n  }\n\n  if (!node.dirty && node.lastCleanEpoch === epoch) {\n    // Even non-live consumers can skip polling if they previously found themselves to be clean at\n    // the current epoch, since their dependencies could not possibly have changed (such a change\n    // would've increased the epoch).\n    return;\n  }\n\n  if (!node.producerMustRecompute(node) && !consumerPollProducersForChange(node)) {\n    // None of our producers report a change since the last time they were read, so no\n    // recomputation of our value is necessary, and we can consider ourselves clean.\n    producerMarkClean(node);\n    return;\n  }\n\n  node.producerRecomputeValue(node);\n\n  // After recomputing the value, we're no longer dirty.\n  producerMarkClean(node);\n}\n\n/**\n * Propagate a dirty notification to live consumers of this producer.\n */\nexport function producerNotifyConsumers(node: ReactiveNode): void {\n  if (node.liveConsumerNode === undefined) {\n    return;\n  }\n\n  // Prevent signal reads when we're updating the graph\n  const prev = inNotificationPhase;\n  inNotificationPhase = true;\n  try {\n    for (const consumer of node.liveConsumerNode) {\n      if (!consumer.dirty) {\n        consumerMarkDirty(consumer);\n      }\n    }\n  } finally {\n    inNotificationPhase = prev;\n  }\n}\n\n/**\n * Whether this `ReactiveNode` in its producer capacity is currently allowed to initiate updates,\n * based on the current consumer context.\n */\nexport function producerUpdatesAllowed(): boolean {\n  return activeConsumer?.consumerAllowSignalWrites !== false;\n}\n\nexport function consumerMarkDirty(node: ReactiveNode): void {\n  node.dirty = true;\n  producerNotifyConsumers(node);\n  node.consumerMarkedDirty?.(node);\n}\n\nexport function producerMarkClean(node: ReactiveNode): void {\n  node.dirty = false;\n  node.lastCleanEpoch = epoch;\n}\n\n/**\n * Prepare this consumer to run a computation in its reactive context.\n *\n * Must be called by subclasses which represent reactive computations, before those computations\n * begin.\n */\nexport function consumerBeforeComputation(node: ReactiveNode | null): ReactiveNode | null {\n  node && (node.nextProducerIndex = 0);\n  return setActiveConsumer(node);\n}\n\n/**\n * Finalize this consumer's state after a reactive computation has run.\n *\n * Must be called by subclasses which represent reactive computations, after those computations\n * have finished.\n */\nexport function consumerAfterComputation(\n  node: ReactiveNode | null,\n  prevConsumer: ReactiveNode | null,\n): void {\n  setActiveConsumer(prevConsumer);\n\n  if (\n    !node ||\n    node.producerNode === undefined ||\n    node.producerIndexOfThis === undefined ||\n    node.producerLastReadVersion === undefined\n  ) {\n    return;\n  }\n\n  if (consumerIsLive(node)) {\n    // For live consumers, we need to remove the producer -> consumer edge for any stale producers\n    // which weren't dependencies after the recomputation.\n    for (let i = node.nextProducerIndex; i < node.producerNode.length; i++) {\n      producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n    }\n  }\n\n  // Truncate the producer tracking arrays.\n  // Perf note: this is essentially truncating the length to `node.nextProducerIndex`, but\n  // benchmarking has shown that individual pop operations are faster.\n  while (node.producerNode.length > node.nextProducerIndex) {\n    node.producerNode.pop();\n    node.producerLastReadVersion.pop();\n    node.producerIndexOfThis.pop();\n  }\n}\n\n/**\n * Determine whether this consumer has any dependencies which have changed since the last time\n * they were read.\n */\nexport function consumerPollProducersForChange(node: ReactiveNode): boolean {\n  assertConsumerNode(node);\n\n  // Poll producers for change.\n  for (let i = 0; i < node.producerNode.length; i++) {\n    const producer = node.producerNode[i];\n    const seenVersion = node.producerLastReadVersion[i];\n\n    // First check the versions. A mismatch means that the producer's value is known to have\n    // changed since the last time we read it.\n    if (seenVersion !== producer.version) {\n      return true;\n    }\n\n    // The producer's version is the same as the last time we read it, but it might itself be\n    // stale. Force the producer to recompute its version (calculating a new value if necessary).\n    producerUpdateValueVersion(producer);\n\n    // Now when we do this check, `producer.version` is guaranteed to be up to date, so if the\n    // versions still match then it has not changed since the last time we read it.\n    if (seenVersion !== producer.version) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * Disconnect this consumer from the graph.\n */\nexport function consumerDestroy(node: ReactiveNode): void {\n  assertConsumerNode(node);\n  if (consumerIsLive(node)) {\n    // Drop all connections from the graph to this node.\n    for (let i = 0; i < node.producerNode.length; i++) {\n      producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n    }\n  }\n\n  // Truncate all the arrays to drop all connection from this node to the graph.\n  node.producerNode.length =\n    node.producerLastReadVersion.length =\n    node.producerIndexOfThis.length =\n      0;\n  if (node.liveConsumerNode) {\n    node.liveConsumerNode.length = node.liveConsumerIndexOfThis!.length = 0;\n  }\n}\n\n/**\n * Add `consumer` as a live consumer of this node.\n *\n * Note that this operation is potentially transitive. If this node becomes live, then it becomes\n * a live consumer of all of its current producers.\n */\nfunction producerAddLiveConsumer(\n  node: ReactiveNode,\n  consumer: ReactiveNode,\n  indexOfThis: number,\n): number {\n  assertProducerNode(node);\n  if (node.liveConsumerNode.length === 0 && isConsumerNode(node)) {\n    // When going from 0 to 1 live consumers, we become a live consumer to our producers.\n    for (let i = 0; i < node.producerNode.length; i++) {\n      node.producerIndexOfThis[i] = producerAddLiveConsumer(node.producerNode[i], node, i);\n    }\n  }\n  node.liveConsumerIndexOfThis.push(indexOfThis);\n  return node.liveConsumerNode.push(consumer) - 1;\n}\n\n/**\n * Remove the live consumer at `idx`.\n */\nfunction producerRemoveLiveConsumerAtIndex(node: ReactiveNode, idx: number): void {\n  assertProducerNode(node);\n\n  if (typeof ngDevMode !== 'undefined' && ngDevMode && idx >= node.liveConsumerNode.length) {\n    throw new Error(\n      `Assertion error: active consumer index ${idx} is out of bounds of ${node.liveConsumerNode.length} consumers)`,\n    );\n  }\n\n  if (node.liveConsumerNode.length === 1 && isConsumerNode(node)) {\n    // When removing the last live consumer, we will no longer be live. We need to remove\n    // ourselves from our producers' tracking (which may cause consumer-producers to lose\n    // liveness as well).\n    for (let i = 0; i < node.producerNode.length; i++) {\n      producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n    }\n  }\n\n  // Move the last value of `liveConsumers` into `idx`. Note that if there's only a single\n  // live consumer, this is a no-op.\n  const lastIdx = node.liveConsumerNode.length - 1;\n  node.liveConsumerNode[idx] = node.liveConsumerNode[lastIdx];\n  node.liveConsumerIndexOfThis[idx] = node.liveConsumerIndexOfThis[lastIdx];\n\n  // Truncate the array.\n  node.liveConsumerNode.length--;\n  node.liveConsumerIndexOfThis.length--;\n\n  // If the index is still valid, then we need to fix the index pointer from the producer to this\n  // consumer, and update it from `lastIdx` to `idx` (accounting for the move above).\n  if (idx < node.liveConsumerNode.length) {\n    const idxProducer = node.liveConsumerIndexOfThis[idx];\n    const consumer = node.liveConsumerNode[idx];\n    assertConsumerNode(consumer);\n    consumer.producerIndexOfThis[idxProducer] = idx;\n  }\n}\n\nfunction consumerIsLive(node: ReactiveNode): boolean {\n  return node.consumerIsAlwaysLive || (node?.liveConsumerNode?.length ?? 0) > 0;\n}\n\nfunction assertConsumerNode(node: ReactiveNode): asserts node is ConsumerNode {\n  node.producerNode ??= [];\n  node.producerIndexOfThis ??= [];\n  node.producerLastReadVersion ??= [];\n}\n\nfunction assertProducerNode(node: ReactiveNode): asserts node is ProducerNode {\n  node.liveConsumerNode ??= [];\n  node.liveConsumerIndexOfThis ??= [];\n}\n\nfunction isConsumerNode(node: ReactiveNode): node is ConsumerNode {\n  return node.producerNode !== undefined;\n}\n\nexport function runPostProducerCreatedFn(node: ReactiveNode): void {\n  postProducerCreatedFn?.(node);\n}\n\nexport function setPostProducerCreatedFn(fn: ReactiveHookFn | null): ReactiveHookFn | null {\n  const prev = postProducerCreatedFn;\n  postProducerCreatedFn = fn;\n  return prev;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {defaultEquals, ValueEqualityFn} from './equality';\nimport {\n  consumerAfterComputation,\n  consumerBeforeComputation,\n  producerAccessed,\n  producerUpdateValueVersion,\n  REACTIVE_NODE,\n  ReactiveNode,\n  setActiveConsumer,\n  SIGNAL,\n  runPostProducerCreatedFn,\n} from './graph';\n\n// Required as the signals library is in a separate package, so we need to explicitly ensure the\n// global `ngDevMode` type is defined.\ndeclare const ngDevMode: boolean | undefined;\n\n/**\n * A computation, which derives a value from a declarative reactive expression.\n *\n * `Computed`s are both producers and consumers of reactivity.\n */\nexport interface ComputedNode<T> extends ReactiveNode {\n  /**\n   * Current value of the computation, or one of the sentinel values above (`UNSET`, `COMPUTING`,\n   * `ERROR`).\n   */\n  value: T;\n\n  /**\n   * If `value` is `ERRORED`, the error caught from the last computation attempt which will\n   * be re-thrown.\n   */\n  error: unknown;\n\n  /**\n   * The computation function which will produce a new value.\n   */\n  computation: () => T;\n\n  equal: ValueEqualityFn<T>;\n}\n\nexport type ComputedGetter<T> = (() => T) & {\n  [SIGNAL]: ComputedNode<T>;\n};\n\n/**\n * Create a computed signal which derives a reactive value from an expression.\n */\nexport function createComputed<T>(\n  computation: () => T,\n  equal?: ValueEqualityFn<T>,\n): ComputedGetter<T> {\n  const node: ComputedNode<T> = Object.create(COMPUTED_NODE);\n  node.computation = computation;\n\n  if (equal !== undefined) {\n    node.equal = equal;\n  }\n\n  const computed = () => {\n    // Check if the value needs updating before returning it.\n    producerUpdateValueVersion(node);\n\n    // Record that someone looked at this signal.\n    producerAccessed(node);\n\n    if (node.value === ERRORED) {\n      throw node.error;\n    }\n\n    return node.value;\n  };\n\n  (computed as ComputedGetter<T>)[SIGNAL] = node;\n  if (typeof ngDevMode !== 'undefined' && ngDevMode) {\n    const debugName = node.debugName ? ' (' + node.debugName + ')' : '';\n    computed.toString = () => `[Computed${debugName}: ${node.value}]`;\n  }\n\n  runPostProducerCreatedFn(node);\n\n  return computed as unknown as ComputedGetter<T>;\n}\n\n/**\n * A dedicated symbol used before a computed value has been calculated for the first time.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nexport const UNSET: any = /* @__PURE__ */ Symbol('UNSET');\n\n/**\n * A dedicated symbol used in place of a computed signal value to indicate that a given computation\n * is in progress. Used to detect cycles in computation chains.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nexport const COMPUTING: any = /* @__PURE__ */ Symbol('COMPUTING');\n\n/**\n * A dedicated symbol used in place of a computed signal value to indicate that a given computation\n * failed. The thrown error is cached until the computation gets dirty again.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nexport const ERRORED: any = /* @__PURE__ */ Symbol('ERRORED');\n\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst COMPUTED_NODE = /* @__PURE__ */ (() => {\n  return {\n    ...REACTIVE_NODE,\n    value: UNSET,\n    dirty: true,\n    error: null,\n    equal: defaultEquals,\n    kind: 'computed',\n\n    producerMustRecompute(node: ComputedNode<unknown>): boolean {\n      // Force a recomputation if there's no current value, or if the current value is in the\n      // process of being calculated (which should throw an error).\n      return node.value === UNSET || node.value === COMPUTING;\n    },\n\n    producerRecomputeValue(node: ComputedNode<unknown>): void {\n      if (node.value === COMPUTING) {\n        // Our computation somehow led to a cyclic read of itself.\n        throw new Error(\n          typeof ngDevMode !== 'undefined' && ngDevMode ? 'Detected cycle in computations.' : '',\n        );\n      }\n\n      const oldValue = node.value;\n      node.value = COMPUTING;\n\n      const prevConsumer = consumerBeforeComputation(node);\n      let newValue: unknown;\n      let wasEqual = false;\n      try {\n        newValue = node.computation();\n        // We want to mark this node as errored if calling `equal` throws; however, we don't want\n        // to track any reactive reads inside `equal`.\n        setActiveConsumer(null);\n        wasEqual =\n          oldValue !== UNSET &&\n          oldValue !== ERRORED &&\n          newValue !== ERRORED &&\n          node.equal(oldValue, newValue);\n      } catch (err) {\n        newValue = ERRORED;\n        node.error = err;\n      } finally {\n        consumerAfterComputation(node, prevConsumer);\n      }\n\n      if (wasEqual) {\n        // No change to `valueVersion` - old and new values are\n        // semantically equivalent.\n        node.value = oldValue;\n        return;\n      }\n\n      node.value = newValue;\n      node.version++;\n    },\n  };\n})();\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport type {SignalNode} from './signal';\n\nfunction defaultThrowError(): never {\n  throw new Error();\n}\n\nlet throwInvalidWriteToSignalErrorFn: <T>(node: SignalNode<T>) => never = defaultThrowError;\n\nexport function throwInvalidWriteToSignalError<T>(node: SignalNode<T>) {\n  throwInvalidWriteToSignalErrorFn(node);\n}\n\nexport function setThrowInvalidWriteToSignalError(fn: <T>(node: SignalNode<T>) => never): void {\n  throwInvalidWriteToSignalErrorFn = fn;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {defaultEquals, ValueEqualityFn} from './equality';\nimport {throwInvalidWriteToSignalError} from './errors';\nimport {\n  producerAccessed,\n  producerIncrementEpoch,\n  producerNotifyConsumers,\n  producerUpdatesAllowed,\n  REACTIVE_NODE,\n  ReactiveNode,\n  ReactiveHookFn,\n  runPostProducerCreatedFn,\n  SIGNAL,\n} from './graph';\n\n// Required as the signals library is in a separate package, so we need to explicitly ensure the\n// global `ngDevMode` type is defined.\ndeclare const ngDevMode: boolean | undefined;\n\n/**\n * If set, called after `WritableSignal`s are updated.\n *\n * This hook can be used to achieve various effects, such as running effects synchronously as part\n * of setting a signal.\n */\nlet postSignalSetFn: ReactiveHookFn | null = null;\n\nexport interface SignalNode<T> extends ReactiveNode {\n  value: T;\n  equal: ValueEqualityFn<T>;\n}\n\nexport type SignalBaseGetter<T> = (() => T) & {readonly [SIGNAL]: unknown};\nexport type SignalSetter<T> = (newValue: T) => void;\nexport type SignalUpdater<T> = (updateFn: (value: T) => T) => void;\n\n// Note: Closure *requires* this to be an `interface` and not a type, which is why the\n// `SignalBaseGetter` type exists to provide the correct shape.\nexport interface SignalGetter<T> extends SignalBaseGetter<T> {\n  readonly [SIGNAL]: SignalNode<T>;\n}\n\n/**\n * Creates a `Signal` getter, setter, and updater function.\n */\nexport function createSignal<T>(\n  initialValue: T,\n  equal?: ValueEqualityFn<T>,\n): [SignalGetter<T>, SignalSetter<T>, SignalUpdater<T>] {\n  const node: SignalNode<T> = Object.create(SIGNAL_NODE);\n  node.value = initialValue;\n  if (equal !== undefined) {\n    node.equal = equal;\n  }\n  const getter = (() => signalGetFn(node)) as SignalGetter<T>;\n  (getter as any)[SIGNAL] = node;\n  if (typeof ngDevMode !== 'undefined' && ngDevMode) {\n    const debugName = node.debugName ? ' (' + node.debugName + ')' : '';\n    getter.toString = () => `[Signal${debugName}: ${node.value}]`;\n  }\n\n  runPostProducerCreatedFn(node);\n  const set = (newValue: T) => signalSetFn(node, newValue);\n  const update = (updateFn: (value: T) => T) => signalUpdateFn(node, updateFn);\n  return [getter, set, update];\n}\n\nexport function setPostSignalSetFn(fn: ReactiveHookFn | null): ReactiveHookFn | null {\n  const prev = postSignalSetFn;\n  postSignalSetFn = fn;\n  return prev;\n}\n\nexport function signalGetFn<T>(node: SignalNode<T>): T {\n  producerAccessed(node);\n  return node.value;\n}\n\nexport function signalSetFn<T>(node: SignalNode<T>, newValue: T) {\n  if (!producerUpdatesAllowed()) {\n    throwInvalidWriteToSignalError(node);\n  }\n\n  if (!node.equal(node.value, newValue)) {\n    node.value = newValue;\n    signalValueChanged(node);\n  }\n}\n\nexport function signalUpdateFn<T>(node: SignalNode<T>, updater: (value: T) => T): void {\n  if (!producerUpdatesAllowed()) {\n    throwInvalidWriteToSignalError(node);\n  }\n\n  signalSetFn(node, updater(node.value));\n}\n\nexport function runPostSignalSetFn<T>(node: SignalNode<T>): void {\n  postSignalSetFn?.(node);\n}\n\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nexport const SIGNAL_NODE: SignalNode<unknown> = /* @__PURE__ */ (() => {\n  return {\n    ...REACTIVE_NODE,\n    equal: defaultEquals,\n    value: undefined,\n    kind: 'signal',\n  };\n})();\n\nfunction signalValueChanged<T>(node: SignalNode<T>): void {\n  node.version++;\n  producerIncrementEpoch();\n  producerNotifyConsumers(node);\n  postSignalSetFn?.(node);\n}\n"], "names": [], "mappings": ";;;;;;AAaA;;AAEG;AACa,SAAA,aAAa,CAAI,CAAI,EAAE,CAAI,EAAA;IACzC,OAAO,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;AACxB;;ACNA;;;;AAIG;AACH,IAAI,cAAc,GAAwB,IAAI;AAC9C,IAAI,mBAAmB,GAAG,KAAK;AAI/B;;AAEG;AACH,IAAI,KAAK,GAAY,CAAY;AAIjC;;AAEG;AACH,IAAI,qBAAqB,GAA0B,IAAI;AAEvD;;;;AAIG;AACU,MAAA,MAAM,mBAAkC,MAAM,CAAC,QAAQ;AAE9D,SAAU,iBAAiB,CAAC,QAA6B,EAAA;IAC7D,MAAM,IAAI,GAAG,cAAc;IAC3B,cAAc,GAAG,QAAQ;AACzB,IAAA,OAAO,IAAI;AACb;SAEgB,iBAAiB,GAAA;AAC/B,IAAA,OAAO,cAAc;AACvB;SAEgB,qBAAqB,GAAA;AACnC,IAAA,OAAO,mBAAmB;AAC5B;AAMM,SAAU,UAAU,CAAC,KAAc,EAAA;AACvC,IAAA,OAAQ,KAA2B,CAAC,MAAM,CAAC,KAAK,SAAS;AAC3D;AAEa,MAAA,aAAa,GAAiB;AACzC,IAAA,OAAO,EAAE,CAAY;AACrB,IAAA,cAAc,EAAE,CAAY;AAC5B,IAAA,KAAK,EAAE,KAAK;AACZ,IAAA,YAAY,EAAE,SAAS;AACvB,IAAA,uBAAuB,EAAE,SAAS;AAClC,IAAA,mBAAmB,EAAE,SAAS;AAC9B,IAAA,iBAAiB,EAAE,CAAC;AACpB,IAAA,gBAAgB,EAAE,SAAS;AAC3B,IAAA,uBAAuB,EAAE,SAAS;AAClC,IAAA,yBAAyB,EAAE,KAAK;AAChC,IAAA,oBAAoB,EAAE,KAAK;AAC3B,IAAA,IAAI,EAAE,SAAS;AACf,IAAA,qBAAqB,EAAE,MAAM,KAAK;AAClC,IAAA,sBAAsB,EAAE,MAAK,GAAG;AAChC,IAAA,mBAAmB,EAAE,MAAK,GAAG;AAC7B,IAAA,oBAAoB,EAAE,MAAK,GAAG;;AA0IhC;;AAEG;AACG,SAAU,gBAAgB,CAAC,IAAkB,EAAA;IACjD,IAAI,mBAAmB,EAAE;QACvB,MAAM,IAAI,KAAK,CACb,OAAO,SAAS,KAAK,WAAW,IAAI;AAClC,cAAE,CAAwD,sDAAA;cACxD,EAAE,CACP;;AAGH,IAAA,IAAI,cAAc,KAAK,IAAI,EAAE;;QAE3B;;AAGF,IAAA,cAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC;;AAGzC,IAAA,MAAM,GAAG,GAAG,cAAc,CAAC,iBAAiB,EAAE;IAE9C,kBAAkB,CAAC,cAAc,CAAC;AAElC,IAAA,IAAI,GAAG,GAAG,cAAc,CAAC,YAAY,CAAC,MAAM,IAAI,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;;;;;;;;AAQzF,QAAA,IAAI,cAAc,CAAC,cAAc,CAAC,EAAE;YAClC,MAAM,aAAa,GAAG,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC;YACtD,iCAAiC,CAAC,aAAa,EAAE,cAAc,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC;;;;;IAO7F,IAAI,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;;AAE7C,QAAA,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,IAAI;;;QAIvC,cAAc,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,cAAc;cACnE,uBAAuB,CAAC,IAAI,EAAE,cAAc,EAAE,GAAG;cACjD,CAAC;;IAEP,cAAc,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO;AAC5D;AAEA;;;;AAIG;SACa,sBAAsB,GAAA;AACpC,IAAA,KAAK,EAAE;AACT;AAEA;;AAEG;AACG,SAAU,0BAA0B,CAAC,IAAkB,EAAA;IAC3D,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;;;QAGvC;;IAGF,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE;;;;QAIhD;;AAGF,IAAA,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE;;;QAG9E,iBAAiB,CAAC,IAAI,CAAC;QACvB;;AAGF,IAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;;IAGjC,iBAAiB,CAAC,IAAI,CAAC;AACzB;AAEA;;AAEG;AACG,SAAU,uBAAuB,CAAC,IAAkB,EAAA;AACxD,IAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE;QACvC;;;IAIF,MAAM,IAAI,GAAG,mBAAmB;IAChC,mBAAmB,GAAG,IAAI;AAC1B,IAAA,IAAI;AACF,QAAA,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,gBAAgB,EAAE;AAC5C,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;gBACnB,iBAAiB,CAAC,QAAQ,CAAC;;;;YAGvB;QACR,mBAAmB,GAAG,IAAI;;AAE9B;AAEA;;;AAGG;SACa,sBAAsB,GAAA;AACpC,IAAA,OAAO,cAAc,EAAE,yBAAyB,KAAK,KAAK;AAC5D;AAEM,SAAU,iBAAiB,CAAC,IAAkB,EAAA;AAClD,IAAA,IAAI,CAAC,KAAK,GAAG,IAAI;IACjB,uBAAuB,CAAC,IAAI,CAAC;AAC7B,IAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;AAClC;AAEM,SAAU,iBAAiB,CAAC,IAAkB,EAAA;AAClD,IAAA,IAAI,CAAC,KAAK,GAAG,KAAK;AAClB,IAAA,IAAI,CAAC,cAAc,GAAG,KAAK;AAC7B;AAEA;;;;;AAKG;AACG,SAAU,yBAAyB,CAAC,IAAyB,EAAA;IACjE,IAAI,KAAK,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;AACpC,IAAA,OAAO,iBAAiB,CAAC,IAAI,CAAC;AAChC;AAEA;;;;;AAKG;AACa,SAAA,wBAAwB,CACtC,IAAyB,EACzB,YAAiC,EAAA;IAEjC,iBAAiB,CAAC,YAAY,CAAC;AAE/B,IAAA,IACE,CAAC,IAAI;QACL,IAAI,CAAC,YAAY,KAAK,SAAS;QAC/B,IAAI,CAAC,mBAAmB,KAAK,SAAS;AACtC,QAAA,IAAI,CAAC,uBAAuB,KAAK,SAAS,EAC1C;QACA;;AAGF,IAAA,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE;;;AAGxB,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtE,YAAA,iCAAiC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;;;;;;IAOxF,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE;AACxD,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE;AACvB,QAAA,IAAI,CAAC,uBAAuB,CAAC,GAAG,EAAE;AAClC,QAAA,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE;;AAElC;AAEA;;;AAGG;AACG,SAAU,8BAA8B,CAAC,IAAkB,EAAA;IAC/D,kBAAkB,CAAC,IAAI,CAAC;;AAGxB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;QACrC,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC;;;AAInD,QAAA,IAAI,WAAW,KAAK,QAAQ,CAAC,OAAO,EAAE;AACpC,YAAA,OAAO,IAAI;;;;QAKb,0BAA0B,CAAC,QAAQ,CAAC;;;AAIpC,QAAA,IAAI,WAAW,KAAK,QAAQ,CAAC,OAAO,EAAE;AACpC,YAAA,OAAO,IAAI;;;AAIf,IAAA,OAAO,KAAK;AACd;AAEA;;AAEG;AACG,SAAU,eAAe,CAAC,IAAkB,EAAA;IAChD,kBAAkB,CAAC,IAAI,CAAC;AACxB,IAAA,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE;;AAExB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,YAAA,iCAAiC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;;;;IAKxF,IAAI,CAAC,YAAY,CAAC,MAAM;QACtB,IAAI,CAAC,uBAAuB,CAAC,MAAM;YACnC,IAAI,CAAC,mBAAmB,CAAC,MAAM;AAC7B,gBAAA,CAAC;AACL,IAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,QAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAwB,CAAC,MAAM,GAAG,CAAC;;AAE3E;AAEA;;;;;AAKG;AACH,SAAS,uBAAuB,CAC9B,IAAkB,EAClB,QAAsB,EACtB,WAAmB,EAAA;IAEnB,kBAAkB,CAAC,IAAI,CAAC;AACxB,IAAA,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,KAAK,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE;;AAE9D,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,YAAA,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,uBAAuB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;;;AAGxF,IAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,WAAW,CAAC;IAC9C,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;AACjD;AAEA;;AAEG;AACH,SAAS,iCAAiC,CAAC,IAAkB,EAAE,GAAW,EAAA;IACxE,kBAAkB,CAAC,IAAI,CAAC;AAExB,IAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,IAAI,GAAG,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE;AACxF,QAAA,MAAM,IAAI,KAAK,CACb,CAAA,uCAAA,EAA0C,GAAG,CAAA,qBAAA,EAAwB,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAA,WAAA,CAAa,CAC/G;;AAGH,IAAA,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,KAAK,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE;;;;AAI9D,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,YAAA,iCAAiC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC;;;;;IAMxF,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC;AAChD,IAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;AAC3D,IAAA,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC;;AAGzE,IAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE;AAC9B,IAAA,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE;;;IAIrC,IAAI,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE;QACtC,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC;QACrD,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC;QAC3C,kBAAkB,CAAC,QAAQ,CAAC;AAC5B,QAAA,QAAQ,CAAC,mBAAmB,CAAC,WAAW,CAAC,GAAG,GAAG;;AAEnD;AAEA,SAAS,cAAc,CAAC,IAAkB,EAAA;AACxC,IAAA,OAAO,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,EAAE,gBAAgB,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC;AAC/E;AAEA,SAAS,kBAAkB,CAAC,IAAkB,EAAA;AAC5C,IAAA,IAAI,CAAC,YAAY,KAAK,EAAE;AACxB,IAAA,IAAI,CAAC,mBAAmB,KAAK,EAAE;AAC/B,IAAA,IAAI,CAAC,uBAAuB,KAAK,EAAE;AACrC;AAEA,SAAS,kBAAkB,CAAC,IAAkB,EAAA;AAC5C,IAAA,IAAI,CAAC,gBAAgB,KAAK,EAAE;AAC5B,IAAA,IAAI,CAAC,uBAAuB,KAAK,EAAE;AACrC;AAEA,SAAS,cAAc,CAAC,IAAkB,EAAA;AACxC,IAAA,OAAO,IAAI,CAAC,YAAY,KAAK,SAAS;AACxC;AAEM,SAAU,wBAAwB,CAAC,IAAkB,EAAA;AACzD,IAAA,qBAAqB,GAAG,IAAI,CAAC;AAC/B;AAEM,SAAU,wBAAwB,CAAC,EAAyB,EAAA;IAChE,MAAM,IAAI,GAAG,qBAAqB;IAClC,qBAAqB,GAAG,EAAE;AAC1B,IAAA,OAAO,IAAI;AACb;;AC1eA;;AAEG;AACa,SAAA,cAAc,CAC5B,WAAoB,EACpB,KAA0B,EAAA;IAE1B,MAAM,IAAI,GAAoB,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC;AAC1D,IAAA,IAAI,CAAC,WAAW,GAAG,WAAW;AAE9B,IAAA,IAAI,KAAK,KAAK,SAAS,EAAE;AACvB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;;IAGpB,MAAM,QAAQ,GAAG,MAAK;;QAEpB,0BAA0B,CAAC,IAAI,CAAC;;QAGhC,gBAAgB,CAAC,IAAI,CAAC;AAEtB,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,OAAO,EAAE;YAC1B,MAAM,IAAI,CAAC,KAAK;;QAGlB,OAAO,IAAI,CAAC,KAAK;AACnB,KAAC;AAEA,IAAA,QAA8B,CAAC,MAAM,CAAC,GAAG,IAAI;AAC9C,IAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,EAAE;AACnE,QAAA,QAAQ,CAAC,QAAQ,GAAG,MAAM,CAAY,SAAA,EAAA,SAAS,CAAK,EAAA,EAAA,IAAI,CAAC,KAAK,GAAG;;IAGnE,wBAAwB,CAAC,IAAI,CAAC;AAE9B,IAAA,OAAO,QAAwC;AACjD;AAEA;;;AAGG;AACU,MAAA,KAAK,mBAAwB,MAAM,CAAC,OAAO;AAExD;;;;AAIG;AACU,MAAA,SAAS,mBAAwB,MAAM,CAAC,WAAW;AAEhE;;;;AAIG;AACU,MAAA,OAAO,mBAAwB,MAAM,CAAC,SAAS;AAE5D;AACA;AACA;AACA,MAAM,aAAa,mBAAmB,CAAC,MAAK;IAC1C,OAAO;AACL,QAAA,GAAG,aAAa;AAChB,QAAA,KAAK,EAAE,KAAK;AACZ,QAAA,KAAK,EAAE,IAAI;AACX,QAAA,KAAK,EAAE,IAAI;AACX,QAAA,KAAK,EAAE,aAAa;AACpB,QAAA,IAAI,EAAE,UAAU;AAEhB,QAAA,qBAAqB,CAAC,IAA2B,EAAA;;;YAG/C,OAAO,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS;SACxD;AAED,QAAA,sBAAsB,CAAC,IAA2B,EAAA;AAChD,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;;AAE5B,gBAAA,MAAM,IAAI,KAAK,CACb,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,GAAG,iCAAiC,GAAG,EAAE,CACvF;;AAGH,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK;AAC3B,YAAA,IAAI,CAAC,KAAK,GAAG,SAAS;AAEtB,YAAA,MAAM,YAAY,GAAG,yBAAyB,CAAC,IAAI,CAAC;AACpD,YAAA,IAAI,QAAiB;YACrB,IAAI,QAAQ,GAAG,KAAK;AACpB,YAAA,IAAI;AACF,gBAAA,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE;;;gBAG7B,iBAAiB,CAAC,IAAI,CAAC;gBACvB,QAAQ;AACN,oBAAA,QAAQ,KAAK,KAAK;AAClB,wBAAA,QAAQ,KAAK,OAAO;AACpB,wBAAA,QAAQ,KAAK,OAAO;AACpB,wBAAA,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC;;YAChC,OAAO,GAAG,EAAE;gBACZ,QAAQ,GAAG,OAAO;AAClB,gBAAA,IAAI,CAAC,KAAK,GAAG,GAAG;;oBACR;AACR,gBAAA,wBAAwB,CAAC,IAAI,EAAE,YAAY,CAAC;;YAG9C,IAAI,QAAQ,EAAE;;;AAGZ,gBAAA,IAAI,CAAC,KAAK,GAAG,QAAQ;gBACrB;;AAGF,YAAA,IAAI,CAAC,KAAK,GAAG,QAAQ;YACrB,IAAI,CAAC,OAAO,EAAE;SACf;KACF;AACH,CAAC,GAAG;;ACpKJ,SAAS,iBAAiB,GAAA;IACxB,MAAM,IAAI,KAAK,EAAE;AACnB;AAEA,IAAI,gCAAgC,GAAsC,iBAAiB;AAErF,SAAU,8BAA8B,CAAI,IAAmB,EAAA;IACnE,gCAAgC,CAAC,IAAI,CAAC;AACxC;AAEM,SAAU,iCAAiC,CAAC,EAAqC,EAAA;IACrF,gCAAgC,GAAG,EAAE;AACvC;;ACIA;;;;;AAKG;AACH,IAAI,eAAe,GAA0B,IAAI;AAiBjD;;AAEG;AACa,SAAA,YAAY,CAC1B,YAAe,EACf,KAA0B,EAAA;IAE1B,MAAM,IAAI,GAAkB,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC;AACtD,IAAA,IAAI,CAAC,KAAK,GAAG,YAAY;AACzB,IAAA,IAAI,KAAK,KAAK,SAAS,EAAE;AACvB,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK;;IAEpB,MAAM,MAAM,IAAI,MAAM,WAAW,CAAC,IAAI,CAAC,CAAoB;AAC1D,IAAA,MAAc,CAAC,MAAM,CAAC,GAAG,IAAI;AAC9B,IAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,EAAE;AACnE,QAAA,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAU,OAAA,EAAA,SAAS,CAAK,EAAA,EAAA,IAAI,CAAC,KAAK,GAAG;;IAG/D,wBAAwB,CAAC,IAAI,CAAC;AAC9B,IAAA,MAAM,GAAG,GAAG,CAAC,QAAW,KAAK,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC;AACxD,IAAA,MAAM,MAAM,GAAG,CAAC,QAAyB,KAAK,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC;AAC5E,IAAA,OAAO,CAAC,MAAM,EAAE,GAAG,EAAE,MAAM,CAAC;AAC9B;AAEM,SAAU,kBAAkB,CAAC,EAAyB,EAAA;IAC1D,MAAM,IAAI,GAAG,eAAe;IAC5B,eAAe,GAAG,EAAE;AACpB,IAAA,OAAO,IAAI;AACb;AAEM,SAAU,WAAW,CAAI,IAAmB,EAAA;IAChD,gBAAgB,CAAC,IAAI,CAAC;IACtB,OAAO,IAAI,CAAC,KAAK;AACnB;AAEgB,SAAA,WAAW,CAAI,IAAmB,EAAE,QAAW,EAAA;AAC7D,IAAA,IAAI,CAAC,sBAAsB,EAAE,EAAE;QAC7B,8BAA8B,CAAC,IAAI,CAAC;;AAGtC,IAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;AACrC,QAAA,IAAI,CAAC,KAAK,GAAG,QAAQ;QACrB,kBAAkB,CAAC,IAAI,CAAC;;AAE5B;AAEgB,SAAA,cAAc,CAAI,IAAmB,EAAE,OAAwB,EAAA;AAC7E,IAAA,IAAI,CAAC,sBAAsB,EAAE,EAAE;QAC7B,8BAA8B,CAAC,IAAI,CAAC;;IAGtC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AACxC;AAEM,SAAU,kBAAkB,CAAI,IAAmB,EAAA;AACvD,IAAA,eAAe,GAAG,IAAI,CAAC;AACzB;AAEA;AACA;AACA;MACa,WAAW,mBAAwC,CAAC,MAAK;IACpE,OAAO;AACL,QAAA,GAAG,aAAa;AAChB,QAAA,KAAK,EAAE,aAAa;AACpB,QAAA,KAAK,EAAE,SAAS;AAChB,QAAA,IAAI,EAAE,QAAQ;KACf;AACH,CAAC;AAED,SAAS,kBAAkB,CAAI,IAAmB,EAAA;IAChD,IAAI,CAAC,OAAO,EAAE;AACd,IAAA,sBAAsB,EAAE;IACxB,uBAAuB,CAAC,IAAI,CAAC;AAC7B,IAAA,eAAe,GAAG,IAAI,CAAC;AACzB;;;;"}