.welcome-container {
  max-width: 500px;
  margin: 0 auto;
  padding: 20px;
}

.logo-section {
  text-align: center;
  margin-bottom: 30px;

  h1 {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--ion-color-primary);
    margin-bottom: 10px;
  }

  p {
    font-size: 1.1rem;
    color: var(--ion-color-medium);
    margin: 0;
  }
}

.connection-chip {
  display: block;
  margin: 0 auto 20px auto;
  width: fit-content;
}

.join-card {
  margin-bottom: 20px;

  .room-code-input {
    text-transform: uppercase;
    font-weight: bold;
    font-size: 1.2rem;
    text-align: center;
  }

  .join-button {
    margin-top: 20px;
    height: 50px;
    font-weight: bold;
  }
}

.validation-error {
  color: var(--ion-color-danger);
  font-size: 0.8rem;
  margin-top: 5px;
  margin-left: 16px;
}

.reconnect-button {
  margin-bottom: 20px;
}

.info-card {
  ion-item {
    --padding-start: 0;

    ion-icon {
      margin-right: 16px;
    }

    h3 {
      margin: 0 0 4px 0;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: var(--ion-color-medium);
      font-size: 0.9rem;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .welcome-container {
    padding: 10px;
  }

  .logo-section h1 {
    font-size: 2rem;
  }
}

// Dark mode adjustments
@media (prefers-color-scheme: dark) {
  .join-card {
    --background: var(--ion-color-step-50);
  }

  .info-card {
    --background: var(--ion-color-step-50);
  }
}