{"version": 3, "file": "testing.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/platform-browser/testing/src/dom_test_component_renderer.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/platform-browser/testing/src/browser.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT, ɵgetDOM as getDOM} from '@angular/common';\nimport {Inject, Injectable} from '@angular/core';\nimport {TestComponentRenderer} from '@angular/core/testing';\n\n/**\n * A DOM based implementation of the TestComponentRenderer.\n */\n@Injectable()\nexport class DOMTestComponentRenderer extends TestComponentRenderer {\n  constructor(@Inject(DOCUMENT) private _doc: any) {\n    super();\n  }\n\n  override insertRootElement(rootElId: string) {\n    this.removeAllRootElementsImpl();\n    const rootElement = getDOM().getDefaultDocument().createElement('div');\n    rootElement.setAttribute('id', rootElId);\n    this._doc.body.appendChild(rootElement);\n  }\n\n  override removeAllRootElements() {\n    // Check whether the `DOCUMENT` instance retrieved from DI contains\n    // the necessary function to complete the cleanup. In tests that don't\n    // interact with DOM, the `DOCUMENT` might be mocked and some functions\n    // might be missing. For such tests, DOM cleanup is not required and\n    // we skip the logic if there are missing functions.\n    if (typeof this._doc.querySelectorAll === 'function') {\n      this.removeAllRootElementsImpl();\n    }\n  }\n\n  private removeAllRootElementsImpl() {\n    const oldRoots = this._doc.querySelectorAll('[id^=root]');\n    for (let i = 0; i < oldRoots.length; i++) {\n      getDOM().remove(oldRoots[i]);\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {PlatformLocation} from '@angular/common';\nimport {MockPlatformLocation} from '@angular/common/testing';\nimport {\n  APP_ID,\n  createPlatformFactory,\n  NgModule,\n  StaticProvider,\n  ɵinternalProvideZoneChangeDetection as internalProvideZoneChangeDetection,\n  ɵChangeDetectionScheduler as ChangeDetectionScheduler,\n  ɵChangeDetectionSchedulerImpl as ChangeDetectionSchedulerImpl,\n  PlatformRef,\n} from '@angular/core';\nimport {TestComponentRenderer} from '@angular/core/testing';\nimport {BrowserModule, platformBrowser} from '../../index';\nimport {DOMTestComponentRenderer} from './dom_test_component_renderer';\n\n/**\n * Platform for testing\n *\n * @publicApi\n */\nexport const platformBrowserTesting: (extraProviders?: StaticProvider[]) => PlatformRef =\n  createPlatformFactory(platformBrowser, 'browserTesting');\n\n/**\n * NgModule for testing.\n *\n * @publicApi\n */\n@NgModule({\n  exports: [BrowserModule],\n  providers: [\n    {provide: APP_ID, useValue: 'a'},\n    internalProvideZoneChangeDetection({}),\n    {provide: ChangeDetectionScheduler, useExisting: ChangeDetectionSchedulerImpl},\n    {provide: PlatformLocation, useClass: MockPlatformLocation},\n    {provide: TestComponentRenderer, useClass: DOMTestComponentRenderer},\n  ],\n})\nexport class BrowserTestingModule {}\n"], "names": ["getDOM", "internalProvideZoneChangeDetection", "ChangeDetectionScheduler", "ChangeDetectionSchedulerImpl"], "mappings": ";;;;;;;;;;;;;;AAYA;;AAEG;AAEG,MAAO,wBAAyB,SAAQ,qBAAqB,CAAA;AAC3B,IAAA,IAAA;AAAtC,IAAA,WAAA,CAAsC,IAAS,EAAA;AAC7C,QAAA,KAAK,EAAE;QAD6B,IAAI,CAAA,IAAA,GAAJ,IAAI;;AAIjC,IAAA,iBAAiB,CAAC,QAAgB,EAAA;QACzC,IAAI,CAAC,yBAAyB,EAAE;AAChC,QAAA,MAAM,WAAW,GAAGA,OAAM,EAAE,CAAC,kBAAkB,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC;AACtE,QAAA,WAAW,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC;QACxC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC;;IAGhC,qBAAqB,GAAA;;;;;;QAM5B,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,gBAAgB,KAAK,UAAU,EAAE;YACpD,IAAI,CAAC,yBAAyB,EAAE;;;IAI5B,yBAAyB,GAAA;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;AACzD,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACxCA,OAAM,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;;;AA1BrB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,wBAAwB,kBACf,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHADjB,wBAAwB,EAAA,CAAA;;sGAAxB,wBAAwB,EAAA,UAAA,EAAA,CAAA;kBADpC;;0BAEc,MAAM;2BAAC,QAAQ;;;ACM9B;;;;AAIG;AACU,MAAA,sBAAsB,GACjC,qBAAqB,CAAC,eAAe,EAAE,gBAAgB;AAEzD;;;;AAIG;MAWU,oBAAoB,CAAA;kHAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAApB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,YATrB,aAAa,CAAA,EAAA,CAAA;AASZ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,EARpB,SAAA,EAAA;AACT,YAAA,EAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAC;YAChCC,mCAAkC,CAAC,EAAE,CAAC;AACtC,YAAA,EAAC,OAAO,EAAEC,yBAAwB,EAAE,WAAW,EAAEC,6BAA4B,EAAC;AAC9E,YAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,oBAAoB,EAAC;AAC3D,YAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,EAAE,wBAAwB,EAAC;AACrE,SAAA,EAAA,OAAA,EAAA,CAPS,aAAa,CAAA,EAAA,CAAA;;sGASZ,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAVhC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,aAAa,CAAC;AACxB,oBAAA,SAAS,EAAE;AACT,wBAAA,EAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAC;wBAChCF,mCAAkC,CAAC,EAAE,CAAC;AACtC,wBAAA,EAAC,OAAO,EAAEC,yBAAwB,EAAE,WAAW,EAAEC,6BAA4B,EAAC;AAC9E,wBAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,QAAQ,EAAE,oBAAoB,EAAC;AAC3D,wBAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,EAAE,wBAAwB,EAAC;AACrE,qBAAA;AACF,iBAAA;;;;;"}