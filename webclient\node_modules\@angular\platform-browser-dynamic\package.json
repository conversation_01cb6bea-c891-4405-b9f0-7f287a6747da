{"name": "@angular/platform-browser-dynamic", "version": "20.0.6", "description": "Angular - library for using Angular in a web browser with JIT compilation", "author": "angular", "license": "MIT", "engines": {"node": "^20.19.0 || ^22.12.0 || >=24.0.0"}, "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/core": "20.0.6", "@angular/common": "20.0.6", "@angular/compiler": "20.0.6", "@angular/platform-browser": "20.0.6"}, "repository": {"type": "git", "url": "https://github.com/angular/angular.git", "directory": "packages/platform-browser-dynamic"}, "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/platform-server", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/localize", "@angular/service-worker"]}, "sideEffects": ["./fesm2022/platform-browser-dynamic.mjs", "./fesm2022/testing.mjs"], "module": "./fesm2022/platform-browser-dynamic.mjs", "typings": "./index.d.ts", "type": "module", "exports": {"./package.json": {"default": "./package.json"}, ".": {"types": "./index.d.ts", "default": "./fesm2022/platform-browser-dynamic.mjs"}, "./testing": {"types": "./testing/index.d.ts", "default": "./fesm2022/testing.mjs"}}}