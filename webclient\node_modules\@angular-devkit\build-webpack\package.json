{"name": "@angular-devkit/build-webpack", "version": "0.2000.4", "description": "Webpack Builder for Architect", "experimental": true, "main": "src/index.js", "typings": "src/index.d.ts", "builders": "builders.json", "exports": {".": {"types": "./src/index.d.ts", "default": "./src/index.js"}, "./package.json": "./package.json", "./*": "./*.js", "./*.js": "./*.js"}, "dependencies": {"@angular-devkit/architect": "0.2000.4", "rxjs": "7.8.2"}, "peerDependencies": {"webpack": "^5.30.0", "webpack-dev-server": "^5.0.2"}, "keywords": ["Angular CLI", "Angular DevKit", "angular", "devkit", "sdk"], "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "packageManager": "pnpm@9.15.9", "engines": {"node": "^20.19.0 || ^22.12.0 || >=24.0.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "homepage": "https://github.com/angular/angular-cli"}