{"version": 3, "sources": ["../src/ngtsc/file_system/src/invalid_file_system.ts", "../src/ngtsc/file_system/src/util.ts", "../src/ngtsc/file_system/src/helpers.ts", "../src/ngtsc/file_system/src/compiler_host.ts", "../src/ngtsc/file_system/src/logical.ts", "../src/ngtsc/file_system/src/ts_read_directory.ts"], "mappings": ";;;;;;AAiBM,IAAO,oBAAP,MAAwB;EAC5B,OAAO,MAAoB;AACzB,UAAM,UAAS;EACjB;EACA,SAAS,MAAoB;AAC3B,UAAM,UAAS;EACjB;EACA,eAAe,MAAoB;AACjC,UAAM,UAAS;EACjB;EACA,UAAU,MAAsB,MAA2B,WAAmB;AAC5E,UAAM,UAAS;EACjB;EACA,WAAW,MAAoB;AAC7B,UAAM,UAAS;EACjB;EACA,QAAQ,QAAwB,MAAoB;AAClD,UAAM,UAAS;EACjB;EACA,QAAQ,MAAoB;AAC1B,UAAM,UAAS;EACjB;EACA,MAAM,MAAoB;AACxB,UAAM,UAAS;EACjB;EACA,KAAK,MAAoB;AACvB,UAAM,UAAS;EACjB;EACA,MAAG;AACD,UAAM,UAAS;EACjB;EACA,MAAM,MAAoB;AACxB,UAAM,UAAS;EACjB;EACA,QAAQ,MAAkC;AACxC,UAAM,UAAS;EACjB;EACA,SAAS,MAAsB,IAAkB;AAC/C,UAAM,UAAS;EACjB;EACA,SAAS,MAAsB,IAAkB;AAC/C,UAAM,UAAS;EACjB;EACA,UAAU,MAAoB;AAC5B,UAAM,UAAS;EACjB;EACA,WAAW,MAAoB;AAC7B,UAAM,UAAS;EACjB;EACA,kBAAe;AACb,UAAM,UAAS;EACjB;EACA,WAAW,OAAe;AACxB,UAAM,UAAS;EACjB;EACA,QAA8B,MAAO;AACnC,UAAM,UAAS;EACjB;EACA,KAA2B,aAAgB,OAAe;AACxD,UAAM,UAAS;EACjB;EACA,OAAO,MAAoB;AACzB,UAAM,UAAS;EACjB;EACA,SAAS,MAAY;AACnB,UAAM,UAAS;EACjB;EACA,SAA+B,MAAS,IAAK;AAC3C,UAAM,UAAS;EACjB;EACA,SAAS,UAAkB,WAAkB;AAC3C,UAAM,UAAS;EACjB;EACA,SAAS,UAAwB;AAC/B,UAAM,UAAS;EACjB;EACA,wBAAqB;AACnB,UAAM,UAAS;EACjB;EACA,UAAgC,MAAO;AACrC,UAAM,UAAS;EACjB;;AAGF,SAAS,YAAS;AAChB,SAAO,IAAI,MACT,+FAA+F;AAEnG;;;AC/FA,IAAM,sBAAsB;AAKtB,SAAU,oBAAoB,MAAY;AAE9C,SAAO,KAAK,QAAQ,OAAO,GAAG;AAChC;AAKM,SAAU,eAAqC,MAAO;AAC1D,SAAO,KAAK,QAAQ,qBAAqB,EAAE;AAC7C;AAEM,SAAU,qBAAqB,SAAqB,UAAwB;AAChF,QAAM,KAAK,QAAQ,cAAc,QAAQ;AACzC,MAAI,OAAO,QAAW;AACpB,UAAM,IAAI,MACR,6BAA6B,mCAAmC,QAC7D,eAAc,EACd,IAAI,CAACA,QAAOA,IAAG,QAAQ,EACvB,KAAK,IAAI,GAAG;EAEnB;AACA,SAAO;AACT;;;ACzBA,IAAI,KAAiB,IAAI,kBAAiB;AACpC,SAAU,gBAAa;AAC3B,SAAO;AACT;AACM,SAAU,cAAc,YAAsB;AAClD,OAAK;AACP;AAKM,SAAU,aAAa,MAAY;AACvC,MAAI,CAAC,GAAG,SAAS,IAAI,GAAG;AACtB,UAAM,IAAI,MAAM,gCAAgC,6BAA6B;EAC/E;AACA,SAAO,GAAG,QAAQ,IAAI;AACxB;AAEA,IAAM,gBAAgB,OAAO,cAAc;AAKrC,SAAU,uBAAuB,IAAsB;AAC3D,QAAM,cAAc;AAEpB,MAAI,YAAY,mBAAmB,QAAW;AAC5C,gBAAY,iBAAiB,GAAG,QAAQ,YAAY,QAAQ;EAC9D;AAIA,SAAO,YAAY;AACrB;AAKM,SAAU,aAAa,MAAY;AACvC,QAAM,aAAa,oBAAoB,IAAI;AAC3C,MAAI,GAAG,SAAS,UAAU,GAAG;AAC3B,UAAM,IAAI,MAAM,gCAAgC,6BAA6B;EAC/E;AACA,SAAO;AACT;AAKM,SAAU,QAA8B,MAAO;AACnD,SAAO,GAAG,QAAQ,IAAI;AACxB;AAKM,SAAU,KAA2B,aAAgB,OAAe;AACxE,SAAO,GAAG,KAAK,UAAU,GAAG,KAAK;AACnC;AAKM,SAAU,QAAQ,aAAqB,OAAe;AAC1D,SAAO,GAAG,QAAQ,UAAU,GAAG,KAAK;AACtC;AAGM,SAAU,OAAO,MAAoB;AACzC,SAAO,GAAG,OAAO,IAAI;AACvB;AAKM,SAAU,SAAS,MAAY;AACnC,SAAO,GAAG,SAAS,IAAI;AACzB;AAKM,SAAU,SAA+B,MAAS,IAAK;AAC3D,SAAO,GAAG,SAAS,MAAM,EAAE;AAC7B;AAKM,SAAU,SAAS,UAAsB,WAAkB;AAC/D,SAAO,GAAG,SAAS,UAAU,SAAS;AACxC;AAQM,SAAU,oBAAoB,cAAoB;AACtD,SAAO,CAAC,SAAS,YAAY,KAAK,CAAC,aAAa,WAAW,IAAI;AACjE;AAOM,SAAU,iBACd,cAA0C;AAE1C,SAAO,oBAAoB,YAAY,IAAK,KAAK,iBAAiC;AACpF;;;ACpHA,YAAY,QAAQ;AACpB,OAAO,QAAQ;AAKT,IAAO,oBAAP,MAAwB;EAEhB;EACA;EAFZ,YACYC,KACA,UAA8B,CAAA,GAAE;AADhC,SAAA,KAAAA;AACA,SAAA,UAAA;EACT;EAEH,cAAc,UAAkB,iBAAgC;AAC9D,UAAM,OAAO,KAAK,SAAS,QAAQ;AACnC,WAAO,SAAS,SACZ,GAAG,iBAAiB,UAAU,MAAM,iBAAiB,IAAI,IACzD;EACN;EAEA,sBAAsB,SAA2B;AAC/C,WAAO,KAAK,GAAG,KAAK,KAAK,sBAAqB,GAAI,GAAG,sBAAsB,OAAO,CAAC;EACrF;EAEA,wBAAqB;AACnB,WAAO,KAAK,GAAG,sBAAqB;EACtC;EAEA,UACE,UACA,MACA,oBACA,SACA,aAA0C;AAE1C,UAAM,OAAO,aAAa,QAAQ;AAClC,SAAK,GAAG,UAAU,KAAK,GAAG,QAAQ,IAAI,CAAC;AACvC,SAAK,GAAG,UAAU,MAAM,IAAI;EAC9B;EAEA,sBAAmB;AACjB,WAAO,KAAK,GAAG,IAAG;EACpB;EAEA,qBAAqB,UAAgB;AACnC,WAAO,KAAK,0BAAyB,IAAK,WAAW,SAAS,YAAW;EAC3E;EAEA,4BAAyB;AACvB,WAAO,KAAK,GAAG,gBAAe;EAChC;EAEA,aAAU;AACR,YAAQ,KAAK,QAAQ,SAAS;MAC5B,KAAK,GAAG,YAAY;AAClB,eAAO;MACT,KAAK,GAAG,YAAY;AAClB,eAAO;MACT;AACE,eAAU;IACd;EACF;EAEA,WAAW,UAAgB;AACzB,UAAM,UAAU,KAAK,GAAG,QAAQ,QAAQ;AACxC,WAAO,KAAK,GAAG,OAAO,OAAO,KAAK,KAAK,GAAG,KAAK,OAAO,EAAE,OAAM;EAChE;EAEA,SAAS,UAAgB;AACvB,UAAM,UAAU,KAAK,GAAG,QAAQ,QAAQ;AACxC,QAAI,CAAC,KAAK,WAAW,OAAO,GAAG;AAC7B,aAAO;IACT;AACA,WAAO,KAAK,GAAG,SAAS,OAAO;EACjC;EAEA,SAAS,MAAY;AACnB,WAAO,KAAK,GAAG,SAAS,KAAK,GAAG,QAAQ,IAAI,CAAC;EAC/C;;;;AC1DK,IAAM,qBAAqB;EAOhC,qBAAqB,SAAU,MAA0B,IAAsB;AAC7E,UAAM,eAAe,SAAS,QAAQ,QAAQ,IAAI,CAAC,GAAG,QAAQ,EAAE,CAAC;AACjE,WAAO,iBAAiB,YAAY;EACtC;;AAOI,IAAO,oBAAP,MAAwB;EAoBlB;EAhBF;EAMA;EAMA,QAAwD,oBAAI,IAAG;EAEvE,YACE,UACQ,cAA2D;AAA3D,SAAA,eAAA;AAIR,SAAK,WAAW,SAAS,OAAO,CAAA,CAAE,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,SAAS,EAAE,MAAM;AACtE,SAAK,oBAAoB,KAAK,SAAS,IACrC,CAAC,QAAQ,KAAK,aAAa,qBAAqB,GAAG,CAAmB;EAE1E;EAQA,gBAAgB,IAAiB;AAC/B,WAAO,KAAK,kBAAkB,uBAAuB,EAAE,CAAC;EAC1D;EAQA,kBAAkB,cAA4B;AAC5C,QAAI,CAAC,KAAK,MAAM,IAAI,YAAY,GAAG;AACjC,YAAM,oBAAoB,KAAK,aAAa,qBAC1C,YAAY;AAEd,UAAI,cAAyC;AAC7C,eAAS,IAAI,GAAG,IAAI,KAAK,SAAS,QAAQ,KAAK;AAC7C,cAAM,UAAU,KAAK,SAAS;AAC9B,cAAM,mBAAmB,KAAK,kBAAkB;AAChD,YAAI,iBAAiB,kBAAkB,iBAAiB,GAAG;AAGzD,wBAAc,KAAK,yBAAyB,cAAc,OAAO;AAEjE,cAAI,YAAY,QAAQ,gBAAgB,MAAM,IAAI;AAChD,0BAAc;UAChB,OAAO;AACL;UACF;QACF;MACF;AACA,WAAK,MAAM,IAAI,cAAc,WAAW;IAC1C;AACA,WAAO,KAAK,MAAM,IAAI,YAAY;EACpC;EAEQ,yBACN,MACA,SAAuB;AAEvB,UAAM,cAAc,eAAe,KAAK,MAAM,QAAQ,MAAM,CAAC;AAC7D,WAAQ,YAAY,WAAW,GAAG,IAAI,cAAc,MAAM;EAC5D;;AAOF,SAAS,iBAAiB,MAAsB,MAAoB;AAClE,SAAO,oBAAoB,SAAS,MAAM,IAAI,CAAC;AACjD;;;ACpHA,OAAOC,SAAQ;AA8BT,SAAU,kCACdC,KAAc;AAEd,MAAID,IAAG,eAAe,QAAW;AAC/B,UAAM,MACJ,sOAEgF;EAEpF;AAEA,QAAM,eAAeA,IAAG,WAAW,KAAKA,GAAE;AAE1C,SAAO,CACL,SACA,YACA,UACA,UACA,UACY;AACZ,UAAM,kBAAkB,CAAC,MAAa;AACpC,YAAM,eAAeC,IAAG,QAAQ,CAAC;AACjC,aAAOA,IAAG,OAAO,YAAY,KAAKA,IAAG,KAAK,YAAY,EAAE,YAAW;IACrE;AAEA,WAAO,aACL,SACA,YACA,UACA,UACAA,IAAG,gBAAe,GAClBA,IAAG,IAAG,GACN,OACA,CAAC,MAAK;AACJ,YAAM,eAAeA,IAAG,QAAQ,CAAC;AAGjC,UAAI,CAAC,gBAAgB,YAAY,GAAG;AAClC,eAAO,EAAC,aAAa,CAAA,GAAI,OAAO,CAAA,EAAE;MACpC;AAEA,YAAM,WAAWA,IAAG,QAAQ,YAAY;AACxC,YAAM,QAAkB,CAAA;AACxB,YAAM,cAAwB,CAAA;AAE9B,iBAAW,SAAS,UAAU;AAC5B,YAAIA,IAAG,KAAKA,IAAG,KAAK,cAAc,KAAK,CAAC,GAAG,YAAW,GAAI;AACxD,sBAAY,KAAK,KAAK;QACxB,OAAO;AACL,gBAAM,KAAK,KAAK;QAClB;MACF;AAEA,aAAO,EAAC,OAAO,YAAW;IAC5B,GACA,CAAC,MAAMA,IAAG,QAAQ,CAAC,GACnB,CAAC,MAAM,gBAAgB,CAAC,CAAC;EAE7B;AACF;", "names": ["sf", "fs", "ts", "fs"]}