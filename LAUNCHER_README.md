# Jugshine Launcher Scripts

This directory contains launcher scripts to easily start the Jugshine game server and web client locally.

## 🚀 Quick Start

### Windows (Batch File)
```bash
# Double-click or run from command prompt
start-jugshine.bat
```

### Windows/Mac/Linux (PowerShell)
```powershell
# Run from PowerShell
.\start-jugshine.ps1

# With custom ports
.\start-jugshine.ps1 -ServerPort 3001 -ClientPort 8101

# Skip dependency installation (faster startup)
.\start-jugshine.ps1 -SkipDependencies
```

## 🔧 Configuration Check

Before running the game, you can verify your configuration:

```bash
# Windows only
check-config.bat
```

This will check:
- ✅ All configuration files exist
- ✅ Dependencies are installed
- ✅ MongoDB is available
- ✅ Server/client URLs are correctly configured

## 📋 What the Launcher Does

1. **Prerequisites Check**
   - Verifies Node.js and npm are installed
   - Installs Ionic CLI if missing

2. **Environment Setup**
   - Creates `.env` file for server from example
   - Installs dependencies for both server and web client

3. **Database Setup**
   - Seeds the MongoDB database with game content
   - Creates prompts and responses for Poisoning Pigeons

4. **Service Startup**
   - Starts Express.js server on port 3000
   - Starts Ionic web client on port 8100
   - Opens web client in your default browser

5. **Monitoring**
   - Displays status messages
   - Keeps services running until you stop them

## 🌐 Server Configuration

### Default URLs
- **Server**: `http://localhost:3000`
- **Web Client**: `http://localhost:8100`
- **API**: `http://localhost:3000/api`

### Changing Server URL

#### For Web Client
Edit `webclient/src/environments/environment.ts`:
```typescript
export const environment = {
  production: false,
  apiUrl: 'http://localhost:3000/api',     // Change port here
  socketUrl: 'http://localhost:3000',     // Change port here
  serverPort: 3000,
  clientPort: 8100
};
```

#### For Godot Client
The Godot client automatically detects the environment:
- **Development builds**: Uses `http://localhost:3000`
- **Release builds**: Uses production URL

To manually change, edit `scripts/ServerConfig.gd`:
```gdscript
const DEV_SERVER_URL = "http://localhost:3000"  # Change this
const DEV_SERVER_PORT = 3000                    # Change this
```

#### For Server
Edit `server/.env`:
```env
PORT=3000                                        # Change this
MONGODB_URI=mongodb://localhost:27017/jugshine
```

## 🎮 Game Flow

1. **Start Services**
   ```bash
   start-jugshine.bat
   ```

2. **Open Godot**
   - Import the Jugshine project
   - Run the project (F5)
   - Navigate to "Poisoning Pigeons"
   - Note the 4-letter room code displayed

3. **Join on Mobile/Browser**
   - Open `http://localhost:8100`
   - Enter the room code
   - Enter your player name
   - Wait for game leader to start

4. **Play the Game**
   - Follow the on-screen instructions
   - Have fun!

## 🛠️ Troubleshooting

### Common Issues

**Port Already in Use**
```bash
# Change ports in the launcher
.\start-jugshine.ps1 -ServerPort 3001 -ClientPort 8101
```

**MongoDB Connection Error**
```bash
# Start MongoDB manually
mongod

# Or install MongoDB Community Edition
# https://www.mongodb.com/try/download/community
```

**Dependencies Not Installing**
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf server/node_modules webclient/node_modules
start-jugshine.bat
```

**Godot Can't Connect to Server**
- Check that the server is running on port 3000
- Verify the server URL in `scripts/ServerConfig.gd`
- Check Windows Firewall isn't blocking the connection

**Web Client Can't Connect**
- Verify the server is running
- Check browser console for errors
- Try refreshing the page
- Check that ports 3000 and 8100 aren't blocked

### Manual Startup

If the launcher doesn't work, you can start services manually:

**Server**
```bash
cd server
npm install
npm run dev
```

**Web Client**
```bash
cd webclient
npm install
ionic serve
```

**Database Seeding**
```bash
cd server
node scripts/seedContent.js
```

## 📱 Mobile Testing

### Local Network Access

To test on mobile devices on your local network:

1. **Find your IP address**
   ```bash
   # Windows
   ipconfig
   
   # Mac/Linux
   ifconfig
   ```

2. **Update configuration**
   - Change `localhost` to your IP address in environment files
   - Example: `http://*************:3000`

3. **Restart services**
   ```bash
   start-jugshine.bat
   ```

4. **Access from mobile**
   - Open browser on phone
   - Navigate to `http://YOUR_IP:8100`

### Production Deployment

For production deployment, update the production environment:

**Web Client** (`webclient/src/environments/environment.prod.ts`):
```typescript
export const environment = {
  production: true,
  apiUrl: '/api',              // Relative URL
  socketUrl: '',               // Same origin
  serverPort: 80,              // Standard HTTP
  clientPort: 80
};
```

**Godot Client** (`scripts/ServerConfig.gd`):
```gdscript
const PROD_SERVER_URL = "https://your-domain.com"
const PROD_SERVER_PORT = 443
```

## 🎯 Next Steps

1. **Start the launcher**: `start-jugshine.bat`
2. **Open Godot**: Run the Jugshine project
3. **Test locally**: Join game from web client
4. **Invite friends**: Share your local IP for network play
5. **Have fun**: Play Poisoning Pigeons!

---

**Need help?** Check the main README.md for more detailed information about the game architecture and features.
