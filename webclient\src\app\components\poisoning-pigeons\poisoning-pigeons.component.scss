// Common card styles
ion-card {
  margin-bottom: 16px;
}

// Waiting for players
.waiting-card {
  .waiting-content {
    text-align: center;
    padding: 20px 0;
    
    ion-icon {
      margin-bottom: 16px;
    }
    
    h3 {
      margin: 0 0 12px 0;
      font-size: 1.3rem;
      font-weight: 600;
    }
    
    p {
      margin: 8px 0;
      color: var(--ion-color-medium);
      
      strong {
        color: var(--ion-color-primary);
        font-weight: bold;
        font-size: 1.1rem;
      }
    }
  }
}

// Instructions and prompt
.instructions-card {
  .prompt-display {
    text-align: center;
    padding: 20px 0;
    
    h2 {
      margin: 0;
      font-size: 1.4rem;
      font-weight: 600;
      color: var(--ion-color-primary);
      line-height: 1.4;
    }
  }
}

// Response selection
.response-selection-card {
  .response-options {
    margin-bottom: 20px;
  }
  
  .response-button {
    margin-bottom: 12px;
    height: auto;
    min-height: 60px;
    white-space: normal;
    text-align: left;
    
    &.button-outline-primary {
      --border-width: 2px;
    }
  }
  
  .submit-button {
    height: 50px;
    font-weight: bold;
  }
}

// Judge waiting
.judge-waiting-card {
  .judge-waiting-content {
    text-align: center;
    padding: 20px 0;
    
    ion-icon {
      margin-bottom: 16px;
    }
    
    h3 {
      margin: 0 0 12px 0;
      font-size: 1.3rem;
      font-weight: 600;
      color: var(--ion-color-secondary);
    }
    
    p {
      margin: 8px 0;
      color: var(--ion-color-medium);
    }
    
    .response-progress {
      margin-top: 20px;
      
      p {
        margin-bottom: 8px;
        font-weight: 500;
      }
      
      ion-progress-bar {
        height: 8px;
        border-radius: 4px;
      }
    }
  }
}

// Response submitted
.submitted-card {
  .submitted-content {
    text-align: center;
    padding: 20px 0;
    
    ion-icon {
      margin-bottom: 16px;
    }
    
    h3 {
      margin: 0 0 12px 0;
      font-size: 1.3rem;
      font-weight: 600;
      color: var(--ion-color-success);
    }
    
    p {
      margin: 8px 0;
      color: var(--ion-color-medium);
      
      strong {
        color: var(--ion-color-dark);
        font-style: italic;
      }
    }
  }
}

// Judge selection
.judge-selection-card {
  .submitted-responses {
    margin-bottom: 20px;
  }
  
  .response-button {
    margin-bottom: 12px;
    height: auto;
    min-height: 60px;
    white-space: normal;
    text-align: left;
    
    &.button-outline-success {
      --border-width: 2px;
    }
  }
  
  .submit-button {
    height: 50px;
    font-weight: bold;
  }
}

// Waiting for judge
.waiting-judge-card {
  .waiting-judge-content {
    text-align: center;
    padding: 20px 0;
    
    ion-icon {
      margin-bottom: 16px;
    }
    
    h3 {
      margin: 0 0 12px 0;
      font-size: 1.3rem;
      font-weight: 600;
    }
    
    p {
      margin: 8px 0;
      color: var(--ion-color-medium);
      
      strong {
        color: var(--ion-color-warning);
      }
    }
    
    .submitted-responses-preview {
      margin-top: 20px;
      text-align: left;
      
      h4 {
        margin: 0 0 12px 0;
        font-size: 1rem;
        font-weight: 600;
        text-align: center;
      }
      
      .response-preview {
        background: var(--ion-color-light);
        padding: 12px;
        margin-bottom: 8px;
        border-radius: 8px;
        border-left: 4px solid var(--ion-color-primary);
        font-style: italic;
      }
    }
  }
}

// Winner announcement
.winner-card {
  .winner-content {
    text-align: center;
    padding: 20px 0;
    
    ion-icon {
      margin-bottom: 16px;
    }
    
    h3 {
      margin: 0 0 8px 0;
      font-size: 1.3rem;
      font-weight: 600;
      color: var(--ion-color-warning);
    }
    
    h2 {
      margin: 0 0 16px 0;
      font-size: 1.8rem;
      font-weight: bold;
      color: var(--ion-color-success);
    }
    
    .winning-response {
      background: var(--ion-color-success-tint);
      color: var(--ion-color-success-contrast);
      padding: 16px;
      border-radius: 12px;
      margin: 16px 0;
      font-size: 1.1rem;
      font-style: italic;
      font-weight: 500;
    }
    
    p {
      margin: 8px 0;
      color: var(--ion-color-medium);
      font-weight: 500;
    }
  }
}

// Game complete
.game-complete-card {
  .game-complete-content {
    text-align: center;
    padding: 20px 0;
    
    ion-icon {
      margin-bottom: 16px;
    }
    
    h3 {
      margin: 0 0 8px 0;
      font-size: 1.3rem;
      font-weight: 600;
      color: var(--ion-color-success);
    }
    
    h2 {
      margin: 0 0 16px 0;
      font-size: 2rem;
      font-weight: bold;
      color: var(--ion-color-primary);
    }
    
    p {
      margin: 8px 0;
      color: var(--ion-color-medium);
      font-size: 1.1rem;
    }
    
    .leader-options {
      margin-top: 24px;
      
      ion-button {
        margin-bottom: 12px;
        height: 50px;
        font-weight: bold;
      }
    }
    
    .waiting-leader {
      margin-top: 20px;
      color: var(--ion-color-warning);
      font-style: italic;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .response-button {
    font-size: 0.9rem;
    min-height: 50px;
  }
  
  .prompt-display h2 {
    font-size: 1.2rem;
  }
  
  .winner-content h2 {
    font-size: 1.6rem;
  }
}

// Dark mode adjustments
@media (prefers-color-scheme: dark) {
  .response-preview {
    background: var(--ion-color-step-100);
    border-left-color: var(--ion-color-primary-tint);
  }
  
  .winning-response {
    background: var(--ion-color-success-shade);
  }
}
