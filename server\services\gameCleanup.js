const Game = require('../models/Game');

class GameCleanupService {
  constructor() {
    this.interval = null;
    this.cleanupIntervalMs = parseInt(process.env.GAME_CLEANUP_INTERVAL_MS) || 5 * 60 * 1000; // 5 minutes
    this.inactiveTimeoutMs = parseInt(process.env.INACTIVE_GAME_TIMEOUT_MS) || 30 * 60 * 1000; // 30 minutes
  }

  start() {
    console.log('Starting game cleanup service...');
    this.interval = setInterval(() => {
      this.cleanupInactiveGames();
    }, this.cleanupIntervalMs);
  }

  stop() {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
      console.log('Game cleanup service stopped');
    }
  }

  async cleanupInactiveGames() {
    try {
      const cutoffTime = new Date(Date.now() - this.inactiveTimeoutMs);
      
      // Find games that haven't been updated recently
      const inactiveGames = await Game.find({
        updated_at: { $lt: cutoffTime },
        game_status: { $ne: 'Closed' }
      });

      if (inactiveGames.length > 0) {
        console.log(`Found ${inactiveGames.length} inactive games to clean up`);
        
        for (const game of inactiveGames) {
          // Check if any players are still active
          const hasActivePlayers = game.players.some(player => 
            player.last_seen && player.last_seen > cutoffTime
          );

          if (!hasActivePlayers) {
            game.game_status = 'Closed';
            await game.save();
            console.log(`Closed inactive game: ${game.room_code}`);
          }
        }
      }

      // Also clean up very old closed games (older than 24 hours)
      const oldGamesCutoff = new Date(Date.now() - 24 * 60 * 60 * 1000);
      const deletedCount = await Game.deleteMany({
        updated_at: { $lt: oldGamesCutoff },
        game_status: 'Closed'
      });

      if (deletedCount.deletedCount > 0) {
        console.log(`Deleted ${deletedCount.deletedCount} old closed games`);
      }

    } catch (error) {
      console.error('Error during game cleanup:', error);
    }
  }

  // Manual cleanup method
  async cleanupGame(roomCode) {
    try {
      const game = await Game.findByRoomCode(roomCode);
      if (game) {
        game.game_status = 'Closed';
        await game.save();
        console.log(`Manually closed game: ${roomCode}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`Error manually cleaning up game ${roomCode}:`, error);
      return false;
    }
  }
}

module.exports = GameCleanupService;
