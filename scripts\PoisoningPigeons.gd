extends BaseMinigame

# Game state enum
enum GameState {
	WAITING_FOR_PLAYERS,
	READY,
	WAITING_FOR_RESPONSES,
	READY_FOR_JUDGING,
	WINNER_CHOSEN,
	WAITING_FOR_LEADER,
	CLOSED
}

# Game data
var room_code: String = ""
var game_state: GameState = GameState.WAITING_FOR_PLAYERS
var round_number: int = 0
var players: Array = []
var current_judge_index: int = 0
var current_prompt: String = ""
var player_responses: Dictionary = {}
var submitted_responses: Array = []
var current_winner: Dictionary = {}
var player_scores: Dictionary = {}
var prompts_data: Dictionary = {}
var responses_data: Dictionary = {}

# UI references
@onready var room_code_label: Label = $VBoxContainer/GameContent/RoomCodeContainer/RoomCodeLabel
@onready var game_status_label: Label = $VBoxContainer/GameContent/StatusContainer/GameStatusLabel
@onready var round_label: Label = $VBoxContainer/GameContent/StatusContainer/RoundLabel
@onready var prompt_label: Label = $VBoxContainer/GameContent/PromptContainer/PromptLabel
@onready var timer_label: Label = $VBoxContainer/GameContent/TimerContainer/TimerLabel
@onready var responses_container: VBoxContainer = $VBoxContainer/GameContent/ResponsesContainer
@onready var winner_container: VBoxContainer = $VBoxContainer/GameContent/WinnerContainer
@onready var winner_label: Label = $VBoxContainer/GameContent/WinnerContainer/WinnerLabel
@onready var scores_container: VBoxContainer = $VBoxContainer/GameContent/ScoresContainer

# Timer
var game_timer: Timer
var timer_duration: float = 30.0
var timer_remaining: float = 0.0

# HTTP request for server communication
var http_request: HTTPRequest
var polling_timer: Timer
var polling_interval: float = 2.0  # Poll server every 2 seconds

func _ready():
	game_name = "Poisoning Pigeons"
	super._ready()

func setup_game():
	print("Setting up Poisoning Pigeons minigame")

	# Generate room code
	generate_room_code()

	# Load game data
	load_game_data()

	# Setup HTTP request
	setup_http_request()

	# Setup timer
	setup_timer()

	# Initialize UI
	update_ui()

	# Create game object on server
	create_game_object()

func generate_room_code():
	var letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	room_code = ""
	for i in range(4):
		room_code += letters[randi() % letters.length()]
	print("Generated room code: ", room_code)

func load_game_data():
	# Load prompts
	var prompts_file = FileAccess.open("res://assets/prompts.json", FileAccess.READ)
	if prompts_file:
		var prompts_json = prompts_file.get_as_text()
		prompts_file.close()
		prompts_data = JSON.parse_string(prompts_json)

	# Load responses
	var responses_file = FileAccess.open("res://assets/responses.json", FileAccess.READ)
	if responses_file:
		var responses_json = responses_file.get_as_text()
		responses_file.close()
		responses_data = JSON.parse_string(responses_json)

func setup_http_request():
	http_request = HTTPRequest.new()
	add_child(http_request)
	http_request.request_completed.connect(_on_http_request_completed)

func setup_timer():
	game_timer = Timer.new()
	add_child(game_timer)
	game_timer.timeout.connect(_on_timer_timeout)
	game_timer.wait_time = 1.0  # Update every second
	game_timer.autostart = false

	# Setup polling timer for server communication
	polling_timer = Timer.new()
	add_child(polling_timer)
	polling_timer.timeout.connect(_on_polling_timeout)
	polling_timer.wait_time = polling_interval
	polling_timer.autostart = true

func update_ui():
	if room_code_label:
		room_code_label.text = "Room Code: " + room_code

	if game_status_label:
		game_status_label.text = "Status: " + get_state_string()

	if round_label:
		round_label.text = "Round: " + str(round_number)

	if prompt_label:
		prompt_label.text = current_prompt if current_prompt != "" else "Waiting for game to start..."

	if timer_label:
		if timer_remaining > 0:
			timer_label.text = "Time: " + str(int(timer_remaining)) + "s"
		else:
			timer_label.text = ""

func get_state_string() -> String:
	match game_state:
		GameState.WAITING_FOR_PLAYERS:
			return "Waiting for Players"
		GameState.READY:
			return "Ready"
		GameState.WAITING_FOR_RESPONSES:
			return "Waiting for Player Responses"
		GameState.READY_FOR_JUDGING:
			return "Ready for Judging"
		GameState.WINNER_CHOSEN:
			return "Winner Chosen"
		GameState.WAITING_FOR_LEADER:
			return "Waiting for Leader"
		GameState.CLOSED:
			return "Closed"
		_:
			return "Unknown"

func create_game_object():
	var game_object = {
		"timestamp": Time.get_unix_time_from_system(),
		"game_type": "poisoning_pigeons",
		"room_code": room_code,
		"game_status": get_state_string(),
		"round_number": round_number,
		"players": players,
		"current_judge": "",
		"current_prompt": "",
		"player_responses": {},
		"submitted_responses": [],
		"player_scores": {},
		"current_winner": {}
	}

	# Send to server
	send_game_object_to_server(game_object)

func send_game_object_to_server(game_object: Dictionary):
	if not GameSettings.is_connected_to_server:
		print("Not connected to server, cannot create game object")
		return

	var json_string = JSON.stringify(game_object)
	var headers = ["Content-Type: application/json"]

	var error = http_request.request(
		GameSettings.server_url + "/api/games",
		headers,
		HTTPClient.METHOD_POST,
		json_string
	)

	if error != OK:
		print("Failed to send game object to server: ", error)

func _on_http_request_completed(result: int, response_code: int, headers: PackedStringArray, body: PackedByteArray):
	var response_text = body.get_string_from_utf8()

	if response_code == 200 or response_code == 201:
		# Try to parse JSON response
		var json = JSON.new()
		var parse_result = json.parse(response_text)

		if parse_result == OK:
			var server_data = json.data
			if typeof(server_data) == TYPE_DICTIONARY:
				# This is a GET response with game state
				handle_server_game_state(server_data)
			else:
				print("Game object successfully created/updated on server")
		else:
			print("Game object successfully created/updated on server")
	else:
		print("Server request failed: ", response_code, " - ", response_text)

func start_new_round():
	round_number += 1

	# Select judge (round-robin)
	if players.size() > 0:
		current_judge_index = (current_judge_index) % players.size()
		var current_judge = players[current_judge_index]
		print("Judge for round ", round_number, ": ", current_judge.get("name", "Unknown"))

	# Select random prompt
	if prompts_data.has("prompts") and prompts_data["prompts"].size() > 0:
		var prompts_array = prompts_data["prompts"]
		current_prompt = prompts_array[randi() % prompts_array.size()]
		print("Selected prompt: ", current_prompt)

	# Generate responses for each player (except judge)
	generate_player_responses()

	# Update game state
	game_state = GameState.WAITING_FOR_RESPONSES

	# Start timer
	start_timer(30.0)

	# Update UI
	update_ui()

	# Update server
	update_game_on_server()

func generate_player_responses():
	player_responses.clear()

	if not responses_data.has("responses"):
		print("No responses data available")
		return

	var all_responses = responses_data["responses"]

	for player in players:
		var player_name = player.get("name", "")
		if player_name == "" or is_current_judge(player_name):
			continue

		# Generate 5 random responses for this player
		var player_response_set = []
		var used_indices = []

		for i in range(5):
			var response_index = randi() % all_responses.size()
			while response_index in used_indices:
				response_index = randi() % all_responses.size()

			used_indices.append(response_index)
			player_response_set.append(all_responses[response_index])

		player_responses[player_name] = player_response_set
		print("Generated responses for ", player_name, ": ", player_response_set)

func is_current_judge(player_name: String) -> bool:
	if players.size() == 0 or current_judge_index >= players.size():
		return false

	var current_judge = players[current_judge_index]
	return current_judge.get("name", "") == player_name

func start_timer(duration: float):
	timer_remaining = duration
	timer_duration = duration
	game_timer.start()

func _on_timer_timeout():
	timer_remaining -= 1.0
	update_ui()

	if timer_remaining <= 0:
		game_timer.stop()
		handle_timer_expired()

func handle_timer_expired():
	match game_state:
		GameState.WAITING_FOR_RESPONSES:
			print("Response timer expired, moving to judging phase")
			game_state = GameState.READY_FOR_JUDGING
			show_responses_for_judging()
		GameState.READY_FOR_JUDGING:
			print("Judging timer expired, no winner chosen")
			handle_no_winner()

func show_responses_for_judging():
	print("Showing responses for judging")

	# Clear previous response buttons
	clear_response_buttons()

	# Show responses container
	if responses_container:
		responses_container.get_child(0).visible = true  # Title label

		# Create buttons for each submitted response
		for i in range(submitted_responses.size()):
			var response_data = submitted_responses[i]
			var response_button = Button.new()
			response_button.text = response_data.get("response", "")
			response_button.custom_minimum_size = Vector2(600, 60)
			response_button.theme_override_font_sizes/font_size = 16
			response_button.autowrap_mode = TextServer.AUTOWRAP_WORD_SMART

			# Connect button signal with response index
			response_button.pressed.connect(_on_response_selected.bind(i))

			responses_container.add_child(response_button)

	start_timer(30.0)  # 30 seconds for judging
	update_ui()

func clear_response_buttons():
	if not responses_container:
		return

	# Remove all children except the title label (first child)
	var children = responses_container.get_children()
	for i in range(1, children.size()):
		children[i].queue_free()

func _on_response_selected(response_index: int):
	if response_index < 0 or response_index >= submitted_responses.size():
		return

	var selected_response = submitted_responses[response_index]
	var winner_name = selected_response.get("player_name", "Unknown")
	var winning_response = selected_response.get("response", "")

	print("Winner selected: ", winner_name, " with response: ", winning_response)

	# Update winner info
	current_winner = {
		"player_name": winner_name,
		"response": winning_response,
		"round": round_number
	}

	# Award point to winner
	if not player_scores.has(winner_name):
		player_scores[winner_name] = 0
	player_scores[winner_name] += 1

	# Update game state
	game_state = GameState.WINNER_CHOSEN
	game_timer.stop()

	# Show winner
	show_winner()

	# Update server
	update_game_on_server()

func show_winner():
	# Clear response buttons
	clear_response_buttons()

	# Hide responses container title
	if responses_container:
		responses_container.get_child(0).visible = false

	# Show winner
	if winner_label:
		var winner_text = "🏆 WINNER 🏆\n"
		winner_text += current_winner.get("player_name", "Unknown") + "\n"
		winner_text += "\"" + current_winner.get("response", "") + "\""
		winner_label.text = winner_text

	# Update scores display
	update_scores_display()

	# Wait 5 seconds then advance
	await get_tree().create_timer(5.0).timeout
	advance_to_next_round()

func update_scores_display():
	if not scores_container:
		return

	# Clear existing score labels (except title)
	var children = scores_container.get_children()
	for i in range(1, children.size()):
		children[i].queue_free()

	# Add score labels for each player
	for player_name in player_scores:
		var score_label = Label.new()
		score_label.text = player_name + ": " + str(player_scores[player_name]) + " points"
		score_label.theme_override_font_sizes/font_size = 18
		score_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
		scores_container.add_child(score_label)

# Simulate receiving responses from web clients (for testing)
func simulate_player_response(player_name: String, response: String):
	var response_data = {
		"player_name": player_name,
		"response": response,
		"timestamp": Time.get_unix_time_from_system()
	}

	submitted_responses.append(response_data)
	print("Received response from ", player_name, ": ", response)

	# Check if all non-judge players have responded
	var expected_responses = players.size() - 1  # Exclude judge
	if submitted_responses.size() >= expected_responses:
		game_state = GameState.READY_FOR_JUDGING
		game_timer.stop()
		show_responses_for_judging()

# Function to simulate adding players (for testing)
func add_test_player(player_name: String, is_leader: bool = false):
	var player_data = {
		"name": player_name,
		"is_leader": is_leader,
		"score": 0,
		"joined_at": Time.get_unix_time_from_system()
	}

	players.append(player_data)
	player_scores[player_name] = 0

	print("Added player: ", player_name)
	update_ui()

# Function to start the game (simulate leader starting)
func start_game_simulation():
	if players.size() < 2:
		print("Need at least 2 players to start")
		return

	game_state = GameState.READY
	start_new_round()

# Override the start_game function from BaseMinigame
func start_game():
	super.start_game()

	# Add some test players for demonstration
	add_test_player("Alice", true)
	add_test_player("Bob")
	add_test_player("Charlie")

	# Start the game after a short delay
	await get_tree().create_timer(2.0).timeout
	start_game_simulation()

	# Simulate some responses after another delay
	await get_tree().create_timer(5.0).timeout
	simulate_player_response("Bob", "I forgot to wear pants today")
	await get_tree().create_timer(1.0).timeout
	simulate_player_response("Charlie", "My pet rock is having an existential crisis")

func handle_no_winner():
	print("No winner for this round")
	current_winner.clear()
	await get_tree().create_timer(5.0).timeout
	advance_to_next_round()

func advance_to_next_round():
	# Move to next judge
	current_judge_index = (current_judge_index + 1) % players.size()

	# Check if anyone has won the game (5 points)
	for player_name in player_scores:
		if player_scores[player_name] >= 5:
			handle_game_victory(player_name)
			return

	# Start new round
	game_state = GameState.READY
	start_new_round()

func handle_game_victory(winner_name: String):
	print("Game over! Winner: ", winner_name)
	game_state = GameState.WAITING_FOR_LEADER
	update_ui()
	update_game_on_server()

func update_game_on_server():
	var game_object = {
		"room_code": room_code,
		"game_status": get_state_string(),
		"round_number": round_number,
		"players": players,
		"current_judge": players[current_judge_index].get("name", "") if players.size() > current_judge_index else "",
		"current_prompt": current_prompt,
		"player_responses": player_responses,
		"submitted_responses": submitted_responses,
		"player_scores": player_scores,
		"current_winner": current_winner
	}

	send_game_update_to_server(game_object)

func send_game_update_to_server(game_object: Dictionary):
	if not GameSettings.is_connected_to_server:
		return

	var json_string = JSON.stringify(game_object)
	var headers = ["Content-Type: application/json"]

	var error = http_request.request(
		GameSettings.server_url + "/api/games/" + room_code,
		headers,
		HTTPClient.METHOD_PUT,
		json_string
	)

	if error != OK:
		print("Failed to update game on server: ", error)

func _on_polling_timeout():
	# Poll server for game state changes
	if GameSettings.is_connected_to_server and room_code != "":
		poll_server_for_updates()

func poll_server_for_updates():
	var headers = ["Content-Type: application/json"]
	var error = http_request.request(
		GameSettings.server_url + "/api/games/" + room_code,
		headers,
		HTTPClient.METHOD_GET
	)

	if error != OK:
		print("Failed to poll server for updates: ", error)

func handle_server_game_state(server_data: Dictionary):
	# Handle game state changes from server
	var server_status = server_data.get("game_status", "")
	var current_status = get_state_string()

	if server_status != current_status:
		print("Server game state changed: ", server_status)

		# Update local state based on server
		match server_status:
			"Ready":
				if game_state == GameState.WAITING_FOR_PLAYERS:
					game_state = GameState.READY
					start_new_round()
			"Ready for Judging":
				if game_state == GameState.WAITING_FOR_RESPONSES:
					game_state = GameState.READY_FOR_JUDGING
					submitted_responses = server_data.get("submitted_responses", [])
					show_responses_for_judging()
			"Winner Chosen":
				if game_state == GameState.READY_FOR_JUDGING:
					game_state = GameState.WINNER_CHOSEN
					current_winner = server_data.get("current_winner", {})
					show_winner()
			"Closed":
				handle_game_closed()

	# Update players list
	var server_players = server_data.get("players", [])
	if server_players.size() != players.size():
		players = server_players
		# Rebuild player scores dictionary
		for player in players:
			var player_name = player.get("name", "")
			if not player_scores.has(player_name):
				player_scores[player_name] = player.get("score", 0)

		update_ui()

func handle_game_closed():
	print("Game closed by server")
	polling_timer.stop()
	game_timer.stop()

	# Return to main menu after a short delay
	await get_tree().create_timer(2.0).timeout
	GameSettings.change_scene(GameSettings.MAIN_MENU_SCENE)

# Override end_game to stop polling
func end_game():
	super.end_game()
	if polling_timer:
		polling_timer.stop()
	if game_timer:
		game_timer.stop()
