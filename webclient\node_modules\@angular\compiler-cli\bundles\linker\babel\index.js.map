{"version": 3, "sources": ["../../../linker/babel/src/es2015_linker_plugin.ts", "../../../linker/babel/src/ast/babel_ast_factory.ts", "../../../linker/babel/src/ast/babel_ast_host.ts", "../../../linker/babel/src/babel_declaration_scope.ts", "../../../linker/babel/src/babel_plugin.ts", "../../../linker/babel/index.ts"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAQA,SAAwC,SAASA,UAAQ;;;ACDzD,SAAQ,SAAS,SAAQ;AAgBnB,IAAO,kBAAP,MAAsB;EAGhB;EAFV,YAEU,WAAiB;AAAjB,SAAA,YAAA;EACP;EAEH,eAAe,WAAuC,iBAAiC;AAErF,aAAS,IAAI,gBAAgB,SAAS,GAAG,KAAK,GAAG,KAAK;AACpD,YAAM,UAAU,gBAAgB;AAChC,QAAE,WAAW,WAAW,WAAW,QAAQ,SAAQ,GAAI,CAAC,QAAQ,SAAS;IAC3E;EACF;EAEA,qBAAqB,EAAE;EAEvB,iBAAiB,QAAsB,OAAmB;AACxD,WAAO,QAAQ,eAAe,qCAAqC;AACnE,WAAO,EAAE,qBAAqB,KAAK,QAAQ,KAAK;EAClD;EAEA,uBACE,aACA,UACA,cAA0B;AAE1B,YAAQ,UAAU;MAChB,KAAK;MACL,KAAK;MACL,KAAK;AACH,eAAO,EAAE,kBAAkB,UAAU,aAAa,YAAY;MAChE;AACE,eAAO,EAAE,iBAAiB,UAAU,aAAa,YAAY;IACjE;EACF;EAEA,cAAc,EAAE;EAEhB,qBAAqB,QAAsB,MAAsB,MAAa;AAC5E,UAAM,OAAO,EAAE,eAAe,QAAQ,IAAI;AAC1C,QAAI,MAAM;AACR,QAAE,WAAW,MAAM,WAAW,eAA0B,KAAK;IAC/D;AACA,WAAO;EACT;EAEA,oBAAoB,EAAE;EAEtB,oBAAoB,YAA0B,SAAqB;AACjE,WAAO,EAAE,iBAAiB,YAAY,SAAwB,IAAI;EACpE;EAEA,4BAA4B,EAAE;EAE9B,0BACE,cACA,YACA,MAAiB;AAEjB,WAAO,MAAM,EAAE,kBAAkB,SAAS;AAC1C,WAAO,EAAE,oBACP,EAAE,WAAW,YAAY,GACzB,WAAW,IAAI,CAAC,UAAU,EAAE,WAAW,KAAK,CAAC,GAC7C,IAAI;EAER;EAEA,8BACE,YACA,MAAgC;AAEhC,QAAI,EAAE,YAAY,IAAI,GAAG;AACvB,aAAO,MAAM,EAAE,kBAAkB,SAAS;IAC5C;AACA,WAAO,EAAE,wBACP,WAAW,IAAI,CAAC,UAAU,EAAE,WAAW,KAAK,CAAC,GAC7C,IAAI;EAER;EAEA,yBACE,cACA,YACA,MAAiB;AAEjB,WAAO,MAAM,EAAE,kBAAkB,SAAS;AAC1C,UAAM,OAAO,iBAAiB,OAAO,EAAE,WAAW,YAAY,IAAI;AAClE,WAAO,EAAE,mBACP,MACA,WAAW,IAAI,CAAC,UAAU,EAAE,WAAW,KAAK,CAAC,GAC7C,IAAI;EAER;EAEA,mBAAmB,EAAE;EAErB,oBAAoB,EAAE;EAEtB,oBAAoB,KAA0B;AAC5C,WAAO,KAAK,qBACV,EAAE,OAAM,GACR,CAAC,OAAO,QAAQ,WAAW,EAAE,cAAc,GAAG,IAAI,GAAG,GACrD,KAAgB;EAEpB;EAEA,cAAc,OAAmD;AAC/D,QAAI,OAAO,UAAU,UAAU;AAC7B,aAAO,EAAE,cAAc,KAAK;IAC9B,WAAW,OAAO,UAAU,UAAU;AACpC,aAAO,EAAE,eAAe,KAAK;IAC/B,WAAW,OAAO,UAAU,WAAW;AACrC,aAAO,EAAE,eAAe,KAAK;IAC/B,WAAW,UAAU,QAAW;AAC9B,aAAO,EAAE,WAAW,WAAW;IACjC,WAAW,UAAU,MAAM;AACzB,aAAO,EAAE,YAAW;IACtB,OAAO;AACL,YAAM,IAAI,MAAM,oBAAoB,UAAU,OAAO,QAAQ;IAC/D;EACF;EAEA,sBAAsB,EAAE;EAExB,oBAAoB,YAAiD;AACnE,WAAO,EAAE,iBACP,WAAW,IAAI,CAAC,SAAQ;AACtB,YAAM,MAAM,KAAK,SACb,EAAE,cAAc,KAAK,YAAY,IACjC,EAAE,WAAW,KAAK,YAAY;AAClC,aAAO,EAAE,eAAe,KAAK,KAAK,KAAK;IACzC,CAAC,CAAC;EAEN;EAEA,gCAAgC,EAAE;EAElC,qBAAqB,YAA0B,cAAoB;AACjE,WAAO,EAAE,iBAAiB,YAAY,EAAE,WAAW,YAAY,GAAkB,KAAK;EACxF;EAEA,wBAAwB,EAAE;EAE1B,qBAAqB,KAAmB,UAAuC;AAC7E,WAAO,EAAE,yBAAyB,KAAK,KAAK,sBAAsB,QAAQ,CAAC;EAC7E;EAEA,sBAAsB,UAAuC;AAC3D,UAAM,WAAW,SAAS,SAAS,IAAI,CAAC,SAAS,MAC/C,KAAK,kBACH,EAAE,gBAAgB,SAAS,MAAM,SAAS,SAAS,SAAS,CAAC,GAC7D,QAAQ,KAAK,CACd;AAEH,WAAO,EAAE,gBAAgB,UAAU,SAAS,WAAW;EACzD;EAEA,uBAAuB,EAAE;EAEzB,uBAAuB,YAAwB;AAC7C,WAAO,EAAE,gBAAgB,UAAU,UAAU;EAC/C;EAEA,qBAAqB,YAAwB;AAC3C,WAAO,EAAE,gBAAgB,QAAQ,UAAU;EAC7C;EAEA,wBAAwB,EAAE;EAE1B,0BACE,cACA,aACA,MAA6B;AAE7B,WAAO,EAAE,oBAAoB,MAAM;MACjC,EAAE,mBAAmB,EAAE,WAAW,YAAY,GAAG,WAAW;KAC7D;EACH;EAEA,kBACE,MACA,gBAAqC;AAErC,QAAI,mBAAmB,MAAM;AAC3B,aAAO;IACT;AACA,SAAK,MAAM;MAIT,UAAU,eAAe,QAAQ,KAAK,YAAY,eAAe,MAAM;MACvE,OAAO;QACL,MAAM,eAAe,MAAM,OAAO;QAClC,QAAQ,eAAe,MAAM;;MAE/B,KAAK;QACH,MAAM,eAAe,IAAI,OAAO;QAChC,QAAQ,eAAe,IAAI;;;AAG/B,SAAK,QAAQ,eAAe,MAAM;AAClC,SAAK,MAAM,eAAe,IAAI;AAE9B,WAAO;EACT;;AAGF,SAAS,cAAc,MAAkB;AAGvC,SAAO,EAAE,OAAO,IAAI;AACtB;;;AClOA,SAAQ,SAASC,UAAQ;AAOnB,IAAO,eAAP,MAAmB;EACvB,cAAc,MAAkB;AAC9B,QAAIC,GAAE,aAAa,IAAI,GAAG;AACxB,aAAO,KAAK;IACd,WAAWA,GAAE,mBAAmB,IAAI,KAAKA,GAAE,aAAa,KAAK,QAAQ,GAAG;AACtE,aAAO,KAAK,SAAS;IACvB,OAAO;AACL,aAAO;IACT;EACF;EAEA,kBAAkBA,GAAE;EAEpB,mBAAmB,KAAiB;AAClC,WAAO,KAAKA,GAAE,iBAAiB,kBAAkB;AACjD,WAAO,IAAI;EACb;EAEA,mBAAmBA,GAAE;EAErB,oBAAoB,KAAiB;AACnC,WAAO,KAAKA,GAAE,kBAAkB,mBAAmB;AACnD,WAAO,IAAI;EACb;EAEA,iBAAiB,MAAkB;AACjC,WAAOA,GAAE,iBAAiB,IAAI,KAAK,yBAAyB,IAAI;EAClE;EAEA,oBAAoB,MAAkB;AACpC,QAAIA,GAAE,iBAAiB,IAAI,GAAG;AAC5B,aAAO,KAAK;IACd,WAAW,yBAAyB,IAAI,GAAG;AACzC,aAAO,CAAC,KAAK,SAAS;IACxB,OAAO;AACL,YAAM,IAAI,iBAAiB,MAAM,iDAAiD;IACpF;EACF;EAEA,OAAO,MAAkB;AACvB,WAAOA,GAAE,cAAc,IAAI;EAC7B;EAEA,iBAAiBA,GAAE;EAEnB,kBAAkB,OAAmB;AACnC,WAAO,OAAOA,GAAE,mBAAmB,kBAAkB;AACrD,WAAO,MAAM,SAAS,IAAI,CAAC,YAAW;AACpC,aAAO,SAAS,mBAAmB,kCAAkC;AACrE,aAAO,SAAS,oBAAoB,2CAA2C;AAC/E,aAAO;IACT,CAAC;EACH;EAEA,kBAAkBA,GAAE;EAEpB,mBAAmB,KAAiB;AAClC,WAAO,KAAKA,GAAE,oBAAoB,mBAAmB;AAErD,UAAM,SAAS,oBAAI,IAAG;AACtB,eAAW,YAAY,IAAI,YAAY;AACrC,aAAO,UAAUA,GAAE,kBAAkB,uBAAuB;AAC5D,aAAO,SAAS,OAAOA,GAAE,cAAc,eAAe;AACtD,aAAO,SAAS,KAAK,gCAAgC,iBAAiB;AAEtE,YAAM,MAAMA,GAAE,aAAa,SAAS,GAAG,IAAI,SAAS,IAAI,OAAO,SAAS,IAAI;AAC5E,aAAO,IAAI,GAAG,OAAO,SAAS,KAAK;IACrC;AACA,WAAO;EACT;EAEA,qBACE,MAAkB;AAElB,WAAOA,GAAE,WAAW,IAAI,KAAKA,GAAE,0BAA0B,IAAI;EAC/D;EAEA,iBAAiB,IAAgB;AAC/B,WAAO,IAAI,KAAK,sBAAsB,YAAY;AAClD,QAAI,CAACA,GAAE,iBAAiB,GAAG,IAAI,GAAG;AAEhC,aAAO,GAAG;IACZ;AAMA,QAAI,GAAG,KAAK,KAAK,WAAW,GAAG;AAC7B,YAAM,IAAI,iBACR,GAAG,MACH,8EAA8E;IAElF;AACA,UAAM,OAAO,GAAG,KAAK,KAAK;AAC1B,WAAO,MAAMA,GAAE,mBAAmB,gDAAgD;AAGlF,QAAI,KAAK,aAAa,QAAQ,KAAK,aAAa,QAAW;AACzD,YAAM,IAAI,iBAAiB,MAAM,0DAA0D;IAC7F;AAEA,WAAO,KAAK;EACd;EAEA,gBAAgB,IAAgB;AAC9B,WAAO,IAAI,KAAK,sBAAsB,YAAY;AAClD,WAAO,GAAG,OAAO,IAAI,CAAC,UAAS;AAC7B,aAAO,OAAOA,GAAE,cAAc,eAAe;AAC7C,aAAO;IACT,CAAC;EACH;EAEA,mBAAmBA,GAAE;EACrB,YAAY,MAAkB;AAC5B,WAAO,MAAMA,GAAE,kBAAkB,mBAAmB;AACpD,WAAO,KAAK,QAAQA,GAAE,cAAc,eAAe;AACnD,WAAO,KAAK;EACd;EACA,eAAe,MAAkB;AAC/B,WAAO,MAAMA,GAAE,kBAAkB,mBAAmB;AACpD,WAAO,KAAK,UAAU,IAAI,CAAC,QAAO;AAChC,aAAO,KAAK,qBAAqB,mCAAmC;AACpE,aAAO,KAAKA,GAAE,cAAc,8BAA8B;AAC1D,aAAO;IACT,CAAC;EACH;EAEA,SAAS,MAAkB;AACzB,QAAI,KAAK,OAAO,QAAQ,KAAK,SAAS,QAAQ,KAAK,OAAO,MAAM;AAC9D,YAAM,IAAI,iBACR,MACA,qEAAqE;IAEzE;AACA,WAAO;MACL,WAAW,KAAK,IAAI,MAAM,OAAO;MACjC,UAAU,KAAK,IAAI,MAAM;MACzB,UAAU,KAAK;MACf,QAAQ,KAAK;;EAEjB;;AAOF,SAAS,kBACP,GAAwC;AAExC,SAAO,MAAM;AACf;AAMA,SAAS,mBAAmB,GAAiC;AAC3D,SAAO,CAACA,GAAE,gBAAgB,CAAC;AAC7B;AASA,SAAS,+BACP,GAAS;AAET,SAAOA,GAAE,aAAa,CAAC,KAAKA,GAAE,gBAAgB,CAAC,KAAKA,GAAE,iBAAiB,CAAC;AAC1E;AAUA,SAAS,oBAAoB,KAAiB;AAC5C,SAAO,CAACA,GAAE,gBAAgB,GAAG;AAC/B;AAOA,SAAS,yBAAyB,MAAkB;AAClD,SACEA,GAAE,kBAAkB,IAAI,KACxB,KAAK,UACL,KAAK,aAAa,OAClBA,GAAE,iBAAiB,KAAK,QAAQ,MAC/B,KAAK,SAAS,UAAU,KAAK,KAAK,SAAS,UAAU;AAE1D;;;AChNA,SAAkB,SAASC,UAAQ;AAe7B,IAAO,wBAAP,MAA4B;EAMZ;EAApB,YAAoB,kBAAmC;AAAnC,SAAA,mBAAA;EAAsC;EAY1D,oBAAoB,YAAwB;AAE1C,QAAI,oBAAoB;AACxB,WAAOA,GAAE,mBAAmB,iBAAiB,GAAG;AAC9C,0BAAoB,kBAAkB;IACxC;AAEA,QAAI,CAACA,GAAE,aAAa,iBAAiB,GAAG;AACtC,aAAO;IACT;AAIA,UAAM,UAAU,KAAK,iBAAiB,WAAW,kBAAkB,IAAI;AACvE,QAAI,YAAY,QAAW;AACzB,aAAO;IACT;AAKA,UAAM,OAAO,QAAQ,MAAM;AAC3B,QACE,CAAC,KAAK,sBAAqB,KAC3B,CAAC,KAAK,qBAAoB,KAC1B,EAAE,KAAK,UAAS,KAAM,KAAK,KAAK,eAAe,WAC/C;AACA,aAAO;IACT;AAEA,WAAO;EACT;;;;AHhDI,SAAU,yBAAyB,EACvC,YACA,WACG,QAAO,GACU;AACpB,MAAI,aAA8E;AAElF,SAAO;IACL,SAAS;MACP,SAAS;QAIP,MAAM,GAAG,OAAK;AACZ,qBAAW,UAAU;AAGrB,gBAAM,OAAO,MAAM;AACnB,gBAAM,WAAW,KAAK,KAAK,YAAY,KAAK,KAAK;AACjD,cAAI,CAAC,UAAU;AACb,kBAAM,IAAI,MACR,yIAAyI;UAE7I;AACA,gBAAM,YAAY,WAAW,QAAQ,KAAK,KAAK,OAAO,KAAK,QAAQ;AAEnE,gBAAM,oBAAoB,kBAAkB,OAC1C,YACA,QACA,IAAI,aAAY,GAChB,IAAI,gBAAgB,SAAS,GAC7B,OAAO;AAET,uBAAa,IAAI,WAAW,mBAAmB,WAAW,KAAK,IAAI;QACrE;QAMA,OAAI;AACF,wBAAc,UAAU;AACxB,qBAAW,EAAC,eAAe,WAAU,KAAK,WAAW,sBAAqB,GAAI;AAC5E,6BAAiB,eAAe,UAAU;UAC5C;AACA,uBAAa;QACf;;MAOF,eAAe,MAAkC,OAAK;AACpD,YAAI,eAAe,MAAM;AAIvB;QACF;AAEA,YAAI;AACF,gBAAM,aAAa,cAAc,IAAI;AACrC,cAAI,eAAe,MAAM;AACvB;UACF;AACA,gBAAM,OAAO,KAAK,KAAK;AACvB,cAAI,CAAC,WAAW,qBAAqB,UAAU,KAAK,CAAC,kBAAkB,IAAI,GAAG;AAC5E;UACF;AAEA,gBAAM,mBAAmB,IAAI,sBAAsB,KAAK,KAAK;AAC7D,gBAAM,cAAc,WAAW,uBAAuB,YAAY,MAAM,gBAAgB;AAExF,eAAK,YAAY,WAAW;QAC9B,SAAS,GAAP;AACA,gBAAM,OAAO,mBAAmB,CAAC,IAAK,EAAE,OAAkB,KAAK;AAC/D,gBAAM,oBAAoB,MAAM,MAAO,EAAY,SAAS,IAAI;QAClE;MACF;;;AAGN;AAOA,SAAS,iBAAiB,MAAyB,YAAyB;AAC1E,MAAI,KAAK,UAAS,GAAI;AACpB,sBAAkB,MAAM,UAAU;EACpC,OAAO;AACL,uBAAmB,MAAM,UAAU;EACrC;AACF;AAKA,SAAS,mBACP,IACA,YAAyB;AAEzB,QAAM,OAAO,GAAG,IAAI,MAAM;AAC1B,OAAK,iBAAiB,QAAQ,UAAU;AAC1C;AAKA,SAAS,kBAAkB,SAA8B,YAAyB;AAChF,QAAM,OAAO,QAAQ,IAAI,MAAM;AAC/B,QAAM,oBAAoB,KAAK,UAAU,CAAC,cAAc,CAAC,UAAU,oBAAmB,CAAE;AAExF,MAAI,sBAAsB,IAAI;AAC5B,YAAQ,iBAAiB,QAAQ,UAAU;EAC7C,OAAO;AACL,SAAK,mBAAmB,aAAa,UAAU;EACjD;AACF;AAEA,SAAS,cAAc,MAAgC;AACrD,QAAM,SAAS,KAAK,KAAK;AACzB,MAAIC,GAAE,aAAa,MAAM,GAAG;AAC1B,WAAO,OAAO;EAChB,WAAWA,GAAE,mBAAmB,MAAM,KAAKA,GAAE,aAAa,OAAO,QAAQ,GAAG;AAC1E,WAAO,OAAO,SAAS;EACzB,WAAWA,GAAE,mBAAmB,MAAM,KAAKA,GAAE,gBAAgB,OAAO,QAAQ,GAAG;AAC7E,WAAO,OAAO,SAAS;EACzB,OAAO;AACL,WAAO;EACT;AACF;AAKA,SAAS,kBAAkB,OAAe;AACxC,SAAO,MAAM,MAAM,CAAC,SAASA,GAAE,aAAa,IAAI,CAAC;AACnD;AAKA,SAAS,WAAc,KAAa;AAClC,MAAI,QAAQ,MAAM;AAChB,UAAM,IAAI,MAAM,iCAAiC;EACnD;AACF;AAKA,SAAS,cAAiB,KAAa;AACrC,MAAI,QAAQ,MAAM;AAChB,UAAM,IAAI,MAAM,qCAAqC;EACvD;AACF;AAKA,SAAS,oBAAoB,MAAiB,SAAiB,MAAY;AACzE,QAAM,WAAW,KAAK,KAAK,YAAY;AACvC,QAAM,QAAQ,KAAK,IAAI,WAAW,MAAM,OAAO;AAC/C,SAAO,GAAG,aAAa,MAAM;AAC/B;;;AIhKM,SAAU,oBAAoB,KAAgB,SAA+B;AACjF,MAAI,cAAc,CAAC;AAEnB,SAAO,yBAAyB;IAC9B,GAAG;IACH,YAAY,IAAI,iBAAgB;IAChC,QAAQ,IAAI,cAAc,SAAS,IAAI;GACxC;AACH;;;AC5BA,IAAA,gBAAe;", "names": ["t", "t", "t", "t", "t"]}