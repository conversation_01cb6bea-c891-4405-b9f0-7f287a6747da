{"name": "@angular-eslint/builder", "version": "20.1.1", "description": "Angular CLI builder for ESLint", "license": "MIT", "main": "dist/index.js", "types": "dist/index.d.ts", "repository": {"type": "git", "url": "https://github.com/angular-eslint/angular-eslint.git", "directory": "packages/builder"}, "files": ["dist", "!**/*.tsbuildinfo", "package.json", "README.md", "LICENSE", "builders.json"], "dependencies": {"@angular-devkit/architect": ">= 0.2000.0 < 0.2100.0", "@angular-devkit/core": ">= 20.0.0 < 21.0.0"}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": "*"}, "builders": "./builders.json", "gitHead": "e2006e5e9c99e5a943d1a999e0efa5247d29ec24"}