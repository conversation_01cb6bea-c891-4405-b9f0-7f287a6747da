{"version": 3, "sources": ["../src/ngtsc/sourcemaps/src/source_file.ts", "../src/ngtsc/sourcemaps/src/segment_marker.ts", "../src/ngtsc/sourcemaps/src/source_file_loader.ts", "../src/ngtsc/sourcemaps/src/content_origin.ts"], "mappings": ";;;;;;AAOA,SAAQ,QAAQ,cAAkD;AAClE,OAAO,gBAAgB;;;ACmBjB,SAAU,gBAAgB,GAAkB,GAAgB;AAChE,SAAO,EAAE,WAAW,EAAE;AACxB;AAUM,SAAU,cACd,sBACA,QACA,QAAc;AAEd,MAAI,WAAW,GAAG;AAChB,WAAO;EACT;AAEA,MAAI,OAAO,OAAO;AAClB,QAAM,WAAW,OAAO,WAAW;AACnC,SAAO,OAAO,qBAAqB,SAAS,KAAK,qBAAqB,OAAO,MAAM,UAAU;AAC3F;EACF;AACA,SAAO,OAAO,KAAK,qBAAqB,QAAQ,UAAU;AACxD;EACF;AACA,QAAM,SAAS,WAAW,qBAAqB;AAC/C,SAAO,EAAC,MAAM,QAAQ,UAAU,MAAM,OAAS;AACjD;;;AD3CM,SAAU,wBAAwB,UAAgB;AACtD,SAAO,WACJ,sBAAsB,WAAW,eAAe,QAAQ,CAAC,EACzD,QAAQ,SAAS,IAAI;AAC1B;AAEM,IAAO,aAAP,MAAiB;EAaV;EAEA;EAEA;EAEA;EACD;EAZD;EACA;EAET,YAEW,YAEA,UAEA,QAEA,SACD,IAAoB;AAPnB,SAAA,aAAA;AAEA,SAAA,WAAA;AAEA,SAAA,SAAA;AAEA,SAAA,UAAA;AACD,SAAA,KAAA;AAER,SAAK,WAAW,wBAAwB,QAAQ;AAChD,SAAK,uBAAuB,4BAA4B,KAAK,QAAQ;AACrE,SAAK,oBAAoB,KAAK,gBAAe;EAC/C;EAKA,2BAAwB;AACtB,UAAM,UAAU,IAAI,WAAU;AAC9B,UAAM,QAAQ,IAAI,WAAU;AAC5B,UAAM,WAA8B,CAAA;AACpC,UAAM,gBAAgB,KAAK,GAAG,QAAQ,KAAK,UAAU;AAGrD,UAAM,0BAA0B,IAAI,MAAsB,CAAC,UACzD,KAAK,GAAG,SAAS,eAAe,KAAK,CAAC;AAGxC,eAAW,WAAW,KAAK,mBAAmB;AAC5C,YAAM,cAAc,QAAQ,IAC1B,wBAAwB,IAAI,QAAQ,eAAe,UAAU,GAC7D,QAAQ,eAAe,QAAQ;AAEjC,YAAM,eAAiC;QACrC,QAAQ,iBAAiB;QACzB;QACA,QAAQ,gBAAgB;QACxB,QAAQ,gBAAgB;;AAE1B,UAAI,QAAQ,SAAS,QAAW;AAC9B,cAAM,YAAY,MAAM,IAAI,QAAQ,IAAI;AACxC,qBAAa,KAAK,SAAS;MAC7B;AAGA,YAAM,OAAO,QAAQ,iBAAiB;AACtC,aAAO,QAAQ,SAAS,QAAQ;AAC9B,iBAAS,KAAK,CAAA,CAAE;MAClB;AAEA,eAAS,MAAM,KAAK,YAAY;IAClC;AAEA,UAAM,YAA0B;MAC9B,SAAS;MACT,MAAM,KAAK,GAAG,SAAS,eAAe,KAAK,UAAU;MACrD,SAAS,QAAQ;MACjB,OAAO,MAAM;MACb,UAAU,OAAO,QAAQ;MACzB,gBAAgB,QAAQ;;AAE1B,WAAO;EACT;EAUA,oBACE,MACA,QAAc;AAEd,QAAI,KAAK,kBAAkB,WAAW,GAAG;AACvC,aAAO;IACT;AAEA,QAAI;AACJ,QAAI,OAAO,KAAK,qBAAqB,QAAQ;AAC3C,iBAAW,KAAK,qBAAqB,QAAQ;IAC/C,OAAO;AAEL,iBAAW,KAAK,SAAS;IAC3B;AAEA,UAAM,kBAAiC,EAAC,MAAM,QAAQ,UAAU,MAAM,OAAS;AAE/E,QAAI,eAAe,2BACjB,KAAK,mBACL,iBACA,OACA,CAAC;AAEH,QAAI,eAAe,GAAG;AACpB,qBAAe;IACjB;AACA,UAAM,EAAC,iBAAiB,gBAAgB,iBAAgB,IACtD,KAAK,kBAAkB;AACzB,UAAM,SAAS,gBAAgB,WAAW,iBAAiB;AAC3D,UAAM,wBAAwB,cAC5B,eAAe,sBACf,iBACA,MAAM;AAGR,WAAO;MACL,MAAM,eAAe;MACrB,MAAM,sBAAsB;MAC5B,QAAQ,sBAAsB;;EAElC;EAMQ,kBAAe;AACrB,UAAM,WAAW,cACf,KAAK,UAAU,KAAK,OAAO,KAC3B,KAAK,SACL,KAAK,oBAAoB;AAE3B,+BAA2B,QAAQ;AACnC,UAAM,oBAA+B,CAAA;AACrC,aAAS,eAAe,GAAG,eAAe,SAAS,QAAQ,gBAAgB;AACzE,YAAM,cAAc,SAAS;AAC7B,YAAM,UAAU,YAAY;AAC5B,UAAI,QAAQ,kBAAkB,WAAW,GAAG;AAG1C,0BAAkB,KAAK,WAAW;AAClC;MACF;AAoBA,YAAM,gBAAgB,YAAY;AAClC,YAAM,cAAc,cAAc;AAuBlC,UAAI,qBAAqB,2BACvB,QAAQ,mBACR,eACA,OACA,CAAC;AAEH,UAAI,qBAAqB,GAAG;AAC1B,6BAAqB;MACvB;AACA,YAAM,mBACJ,gBAAgB,SACZ,2BACE,QAAQ,mBACR,aACA,MACA,kBAAkB,IAEpB,QAAQ,kBAAkB,SAAS;AAEzC,eACM,mBAAmB,oBACvB,oBAAoB,kBACpB,oBACA;AACA,cAAM,cAAuB,QAAQ,kBAAkB;AACvD,0BAAkB,KAAK,cAAc,MAAM,aAAa,WAAW,CAAC;MACtE;IACF;AACA,WAAO;EACT;;AAcI,SAAU,2BACd,UACA,QACA,WACA,YAAkB;AAElB,MAAI,aAAa,SAAS,SAAS;AACnC,QAAM,OAAO,YAAY,KAAK;AAE9B,MAAI,gBAAgB,SAAS,YAAY,kBAAkB,MAAM,IAAI,MAAM;AAEzE,WAAO;EACT;AAEA,MAAI,gBAAgB;AACpB,SAAO,cAAc,YAAY;AAC/B,UAAM,QAAS,aAAa,cAAe;AAC3C,QAAI,gBAAgB,SAAS,OAAO,kBAAkB,MAAM,KAAK,MAAM;AACrE,sBAAgB;AAChB,mBAAa,QAAQ;IACvB,OAAO;AACL,mBAAa,QAAQ;IACvB;EACF;AACA,SAAO;AACT;AAmBM,SAAU,cAAc,iBAA6B,IAAa,IAAW;AACjF,QAAM,OAAO,GAAG,QAAQ,GAAG;AA0C3B,QAAM,OAAO,gBAAgB,GAAG,kBAAkB,GAAG,eAAe;AACpE,MAAI,OAAO,GAAG;AACZ,WAAO;MACL;MACA,kBAAkB,cAChB,gBAAgB,sBAChB,GAAG,kBACH,IAAI;MAEN,gBAAgB,GAAG;MACnB,iBAAiB,GAAG;;EAExB,OAAO;AACL,WAAO;MACL;MACA,kBAAkB,GAAG;MACrB,gBAAgB,GAAG;MACnB,iBAAiB,cACf,GAAG,eAAe,sBAClB,GAAG,iBACH,CAAC,IAAI;;EAGX;AACF;AAMM,SAAU,cACd,QACA,SACA,qCAA6C;AAE7C,MAAI,WAAW,MAAM;AACnB,WAAO,CAAA;EACT;AAEA,QAAM,cAAc,OAAO,OAAO,QAAQ;AAC1C,MAAI,gBAAgB,MAAM;AACxB,WAAO,CAAA;EACT;AAEA,QAAM,WAAsB,CAAA;AAC5B,WAAS,gBAAgB,GAAG,gBAAgB,YAAY,QAAQ,iBAAiB;AAC/E,UAAM,wBAAwB,YAAY;AAC1C,eAAW,cAAc,uBAAuB;AAC9C,UAAI,WAAW,UAAU,GAAG;AAC1B,cAAM,iBAAiB,QAAQ,WAAW;AAC1C,YAAI,mBAAmB,QAAQ,mBAAmB,QAAW;AAE3D;QACF;AACA,cAAM,kBAAkB,WAAW;AACnC,cAAM,OAAO,WAAW,WAAW,IAAI,OAAO,MAAM,WAAW,MAAM;AACrE,cAAM,OAAO,WAAW;AACxB,cAAM,SAAS,WAAW;AAC1B,cAAM,mBAAkC;UACtC,MAAM;UACN,QAAQ;UACR,UAAU,oCAAoC,iBAAiB;UAC/D,MAAM;;AAER,cAAM,kBAAiC;UACrC;UACA;UACA,UAAU,eAAe,qBAAqB,QAAQ;UACtD,MAAM;;AAER,iBAAS,KAAK,EAAC,MAAM,kBAAkB,iBAAiB,eAAc,CAAC;MACzE;IACF;EACF;AACA,SAAO;AACT;AAUM,SAAU,wBAAwB,UAAmB;AACzD,QAAM,mBAAmB,oBAAI,IAAG;AAChC,aAAW,WAAW,UAAU;AAC9B,UAAM,iBAAiB,QAAQ;AAC/B,QAAI,CAAC,iBAAiB,IAAI,cAAc,GAAG;AACzC,uBAAiB,IAAI,gBAAgB,CAAA,CAAE;IACzC;AACA,UAAM,WAAW,iBAAiB,IAAI,cAAc;AACpD,aAAS,KAAK,QAAQ,eAAe;EACvC;AACA,mBAAiB,QAAQ,CAAC,mBAAmB,eAAe,KAAK,eAAe,CAAC;AACjF,SAAO;AACT;AAQM,SAAU,2BAA2B,UAAmB;AAC5D,QAAM,mBAAmB,wBAAwB,QAAQ;AACzD,mBAAiB,QAAQ,CAAC,YAAW;AACnC,aAAS,IAAI,GAAG,IAAI,QAAQ,SAAS,GAAG,KAAK;AAC3C,cAAQ,GAAG,OAAO,QAAQ,IAAI;IAChC;EACF,CAAC;AACH;AAEM,SAAU,4BAA4B,KAAW;AAMrD,QAAM,wBAAwB;AAC9B,QAAM,cAAc,mBAAmB,GAAG;AAC1C,QAAM,iBAAiB,CAAC,CAAC;AACzB,WAAS,IAAI,GAAG,IAAI,YAAY,SAAS,GAAG,KAAK;AAC/C,mBAAe,KAAK,eAAe,KAAK,YAAY,KAAK,qBAAqB;EAChF;AACA,SAAO;AACT;AAEA,SAAS,mBAAmB,KAAW;AACrC,SAAO,IAAI,MAAM,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM;AAC5C;AASA,IAAM,aAAN,MAAgB;EACN,MAAM,oBAAI,IAAG;EAOZ,OAAY,CAAA;EAQZ,SAAc,CAAA;EAavB,IAAI,KAAQ,OAAQ;AAClB,QAAI,KAAK,IAAI,IAAI,GAAG,GAAG;AACrB,aAAO,KAAK,IAAI,IAAI,GAAG;IACzB;AACA,UAAM,QAAQ,KAAK,OAAO,KAAK,KAAK,IAAI;AACxC,SAAK,KAAK,KAAK,GAAG;AAClB,SAAK,IAAI,IAAI,KAAK,KAAK;AACvB,WAAO;EACT;;AASF,IAAM,aAAN,MAAgB;EACN,MAAM,oBAAI,IAAG;EAMZ,SAAc,CAAA;EAYvB,IAAI,OAAQ;AACV,QAAI,KAAK,IAAI,IAAI,KAAK,GAAG;AACvB,aAAO,KAAK,IAAI,IAAI,KAAK;IAC3B;AACA,UAAM,QAAQ,KAAK,OAAO,KAAK,KAAK,IAAI;AACxC,SAAK,IAAI,IAAI,OAAO,KAAK;AACzB,WAAO;EACT;;AAGF,IAAM,QAAN,MAAW;EAEW;EADZ,MAAM,oBAAI,IAAG;EACrB,YAAoB,WAAmC;AAAnC,SAAA,YAAA;EAAsC;EAC1D,IAAI,OAAY;AACd,QAAI,CAAC,KAAK,IAAI,IAAI,KAAK,GAAG;AACxB,WAAK,IAAI,IAAI,OAAO,KAAK,UAAU,KAAK,CAAC;IAC3C;AACA,WAAO,KAAK,IAAI,IAAI,KAAK;EAC3B;;;;AE/iBF,OAAOA,iBAAgB;;;ACYvB,IAAY;CAAZ,SAAYC,gBAAa;AAIvB,EAAAA,eAAAA,eAAA,cAAA,KAAA;AAIA,EAAAA,eAAAA,eAAA,YAAA,KAAA;AAKA,EAAAA,eAAAA,eAAA,gBAAA,KAAA;AACF,GAdY,kBAAA,gBAAa,CAAA,EAAA;;;ADHzB,IAAM,iBAAiB;AAWjB,IAAO,mBAAP,MAAuB;EAIjB;EACA;EAEA;EANF,eAAiC,CAAA;EAEzC,YACU,IACA,QAEA,WAAyC;AAHzC,SAAA,KAAA;AACA,SAAA,SAAA;AAEA,SAAA,YAAA;EACP;EA6BH,eACE,YACA,WAA0B,MAC1B,aAAgC,MAAI;AAEpC,UAAM,iBAAiB,aAAa,OAAO,cAAc,WAAW,cAAc;AAClF,UAAM,gBAAsC,cAAc;MACxD,QAAQ,cAAc;MACtB,GAAG;;AAEL,WAAO,KAAK,uBAAuB,YAAY,UAAU,gBAAgB,aAAa;EACxF;EAkBQ,uBACN,YACA,UACA,cACA,eAAmC;AAEnC,UAAM,gBAAgB,KAAK,aAAa,MAAK;AAC7C,QAAI;AACF,UAAI,aAAa,MAAM;AACrB,YAAI,CAAC,KAAK,GAAG,OAAO,UAAU,GAAG;AAC/B,iBAAO;QACT;AACA,mBAAW,KAAK,eAAe,UAAU;MAC3C;AAGA,UAAI,kBAAkB,MAAM;AAC1B,wBAAgB,KAAK,cAAc,YAAY,UAAU,YAAY;MACvE;AAEA,UAAI,UAAiC,CAAA;AACrC,UAAI,kBAAkB,MAAM;AAC1B,cAAM,WAAW,cAAc,WAAW;AAC1C,kBAAU,KAAK,eAAe,UAAU,aAAa;MACvD;AAEA,aAAO,IAAI,WAAW,YAAY,UAAU,eAAe,SAAS,KAAK,EAAE;IAC7E,SAAS,GAAP;AACA,WAAK,OAAO,KACV,wBAAwB,yCAA0C,EAAY,SAAS;AAEzF,aAAO;IACT;AAEE,WAAK,eAAe;IACtB;EACF;EAeQ,cACN,YACA,gBACA,cAA2B;AAK3B,UAAM,WAAW,KAAK,oBAAoB,cAAc;AACxD,UAAM,SAASC,YAAW,aAAa,KAAK,QAAQ;AACpD,QAAI,WAAW,MAAM;AACnB,aAAO;QACL,KAAKA,YAAW,YAAY,OAAO,IAAG,CAAG,EAAE;QAC3C,SAAS;QACT,QAAQ,cAAc;;IAE1B;AAEA,QAAI,iBAAiB,cAAc,QAAQ;AAIzC,aAAO;IACT;AAEA,UAAM,WAAWA,YAAW,oBAAoB,KAAK,QAAQ;AAC7D,QAAI,UAAU;AACZ,UAAI;AACF,cAAM,WAAW,SAAS,MAAM,SAAS;AACzC,cAAM,kBAAkB,KAAK,GAAG,QAAQ,KAAK,GAAG,QAAQ,UAAU,GAAG,QAAQ;AAC7E,eAAO;UACL,KAAK,KAAK,iBAAiB,eAAe;UAC1C,SAAS;UACT,QAAQ,cAAc;;MAE1B,SAAS,GAAP;AACA,aAAK,OAAO,KACV,wBAAwB,yCAA0C,EAAY,SAAS;AAEzF,eAAO;MACT;IACF;AAEA,UAAM,iBAAiB,KAAK,GAAG,QAAQ,aAAa,MAAM;AAC1D,QAAI,KAAK,GAAG,OAAO,cAAc,GAAG;AAClC,aAAO;QACL,KAAK,KAAK,iBAAiB,cAAc;QACzC,SAAS;QACT,QAAQ,cAAc;;IAE1B;AAEA,WAAO;EACT;EAMQ,eACN,UACA,EAAC,KAAK,QAAQ,gBAAe,GAAgB;AAE7C,UAAM,aAAa,KAAK,GAAG,QACzB,KAAK,GAAG,QAAQ,QAAQ,GACxB,KAAK,sBAAsB,IAAI,cAAc,EAAE,CAAC;AAElD,WAAO,IAAI,QAAQ,IAAI,CAAC,QAAQ,UAAS;AACvC,YAAM,OAAO,KAAK,GAAG,QAAQ,YAAY,KAAK,sBAAsB,MAAM,CAAC;AAC3E,YAAM,UAAW,IAAI,kBAAkB,IAAI,eAAe,UAAW;AAOrE,YAAM,eACJ,YAAY,QAAQ,oBAAoB,cAAc,WAClD,cAAc,SACd,cAAc;AACpB,aAAO,KAAK,uBAAuB,MAAM,SAAS,cAAc,IAAI;IACtE,CAAC;EACH;EAOQ,eAAe,YAA0B;AAC/C,SAAK,UAAU,UAAU;AACzB,WAAO,KAAK,GAAG,SAAS,UAAU;EACpC;EAQQ,iBAAiB,SAAuB;AAC9C,SAAK,UAAU,OAAO;AACtB,WAAO,KAAK,MAAM,KAAK,GAAG,SAAS,OAAO,CAAC;EAC7C;EAMQ,UAAU,MAAoB;AACpC,QAAI,KAAK,aAAa,SAAS,IAAI,GAAG;AACpC,YAAM,IAAI,MACR,4CAA4C,KAAK,aAAa,KAAK,MAAM,QAAQ,MAAM;IAE3F;AACA,SAAK,aAAa,KAAK,IAAI;EAC7B;EAEQ,oBAAoB,UAAgB;AAC1C,QAAI,0BAA0B,SAAS,SAAS;AAChD,WACE,0BAA0B,MACzB,SAAS,6BAA6B,QAAQ,SAAS,6BAA6B,OACrF;AACA;IACF;AACA,QAAI,oBAAoB,SAAS,YAAY,MAAM,0BAA0B,CAAC;AAC9E,QAAI,sBAAsB,IAAI;AAC5B,0BAAoB;IACtB;AACA,WAAO,SAAS,MAAM,oBAAoB,CAAC;EAC7C;EAUQ,sBAAsB,MAAY;AACxC,WAAO,KAAK,QACV,gBACA,CAAC,GAAW,WAAmB,KAAK,UAAU,OAAO,YAAW,MAAO,EAAE;EAE7E;;", "names": ["mapHelpers", "ContentOrigin", "mapHelpers"]}