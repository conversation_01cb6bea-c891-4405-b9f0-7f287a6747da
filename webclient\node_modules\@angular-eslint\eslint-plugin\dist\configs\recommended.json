{"parser": "@typescript-eslint/parser", "plugins": ["@angular-eslint"], "rules": {"@angular-eslint/contextual-lifecycle": "error", "@angular-eslint/no-empty-lifecycle-method": "error", "@angular-eslint/no-input-rename": "error", "@angular-eslint/no-inputs-metadata-property": "error", "@angular-eslint/no-output-native": "error", "@angular-eslint/no-output-on-prefix": "error", "@angular-eslint/no-output-rename": "error", "@angular-eslint/no-outputs-metadata-property": "error", "@angular-eslint/prefer-inject": "error", "@angular-eslint/prefer-standalone": "error", "@angular-eslint/use-pipe-transform-interface": "error", "@angular-eslint/use-lifecycle-interface": "warn"}}