{"name": "@angular/language-service", "version": "20.0.6", "description": "Angular - language services", "main": "./index.js", "typings": "./index.d.ts", "author": "angular", "license": "MIT", "engines": {"node": "^20.19.0 || ^22.12.0 || >=24.0.0"}, "exports": {".": {"types": "./index.d.ts", "default": "./index.js"}, "./package.json": {"default": "./package.json"}, "./api": {"types": "./api.d.ts", "default": "./api_bundle.js"}}, "repository": {"type": "git", "url": "https://github.com/angular/angular.git", "directory": "packages/language-service"}, "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/platform-server", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/localize", "@angular/service-worker"]}}