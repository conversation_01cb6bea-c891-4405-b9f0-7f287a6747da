extends Node

# Server configuration for Jugshine
# This file contains all server-related settings

# Development server (local)
const DEV_SERVER_URL = "http://localhost:3000"
const DEV_SERVER_PORT = 3000

# Production server (when deployed)
const PROD_SERVER_URL = "https://your-production-server.com"
const PROD_SERVER_PORT = 443

# Current environment
enum Environment {
	DEVELOPMENT,
	PRODUCTION
}

# Set this to DEVELOPMENT for local testing, PRODUCTION for deployed version
var current_environment: Environment = Environment.DEVELOPMENT

# Get the current server URL based on environment
func get_server_url() -> String:
	match current_environment:
		Environment.DEVELOPMENT:
			return DEV_SERVER_URL
		Environment.PRODUCTION:
			return PROD_SERVER_URL
		_:
			return DEV_SERVER_URL

# Get the current server port based on environment
func get_server_port() -> int:
	match current_environment:
		Environment.DEVELOPMENT:
			return DEV_SERVER_PORT
		Environment.PRODUCTION:
			return PROD_SERVER_PORT
		_:
			return DEV_SERVER_PORT

# Get full API URL
func get_api_url() -> String:
	return get_server_url() + "/api"

# Get health check URL
func get_health_url() -> String:
	return get_server_url() + "/health"

# Set environment (useful for switching between dev and prod)
func set_environment(env: Environment):
	current_environment = env
	print("Server environment set to: ", Environment.keys()[env])
	print("Server URL: ", get_server_url())

# Auto-detect environment based on build type (optional)
func auto_detect_environment():
	if OS.is_debug_build():
		current_environment = Environment.DEVELOPMENT
	else:
		current_environment = Environment.PRODUCTION
	
	print("Auto-detected environment: ", Environment.keys()[current_environment])
	print("Server URL: ", get_server_url())
