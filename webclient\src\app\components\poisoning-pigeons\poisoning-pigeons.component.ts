import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Game, Player } from '../../services/game.service';

@Component({
  selector: 'app-poisoning-pigeons',
  templateUrl: './poisoning-pigeons.component.html',
  styleUrls: ['./poisoning-pigeons.component.scss'],
  imports: [CommonModule, IonicModule],
})
export class PoisoningPigeonsComponent implements OnInit {
  @Input() game!: Game;
  @Input() player!: Player | null;
  @Output() actionSubmitted = new EventEmitter<{ type: string; data: any }>();

  selectedResponseIndex: number | null = null;
  selectedWinnerIndex: number | null = null;
  isSubmitting: boolean = false;
  playerResponses: string[] = [];

  ngOnInit() {
    this.loadPlayerResponses();
  }

  ngOnChanges() {
    this.loadPlayerResponses();
    // Reset selections when game state changes
    if (this.game.game_status !== 'Waiting for Player Responses') {
      this.selectedResponseIndex = null;
    }
    if (this.game.game_status !== 'Ready for Judging') {
      this.selectedWinnerIndex = null;
    }
  }

  get isJudge(): boolean {
    return this.game.current_judge === this.player?.name;
  }

  loadPlayerResponses() {
    if (this.game.game_data.player_responses && this.player?.name) {
      this.playerResponses = this.game.game_data.player_responses[this.player.name] || [];
    }
  }

  selectResponse(index: number) {
    if (this.isSubmitting || this.hasSubmittedResponse()) return;
    this.selectedResponseIndex = index;
  }

  async submitResponse() {
    if (this.selectedResponseIndex === null || this.isSubmitting || this.hasSubmittedResponse()) {
      return;
    }

    const selectedResponse = this.playerResponses[this.selectedResponseIndex];
    if (!selectedResponse) return;

    this.isSubmitting = true;

    try {
      this.actionSubmitted.emit({
        type: 'submit_response',
        data: { response: selectedResponse }
      });
    } finally {
      this.isSubmitting = false;
    }
  }

  selectWinner(index: number) {
    if (this.isSubmitting || !this.isJudge) return;
    this.selectedWinnerIndex = index;
  }

  async submitJudgment() {
    if (this.selectedWinnerIndex === null || this.isSubmitting || !this.isJudge) {
      return;
    }

    this.isSubmitting = true;

    try {
      this.actionSubmitted.emit({
        type: 'judge',
        data: { response_index: this.selectedWinnerIndex }
      });
    } finally {
      this.isSubmitting = false;
    }
  }

  hasSubmittedResponse(): boolean {
    if (!this.game.game_data.submitted_responses || !this.player?.name) {
      return false;
    }

    return this.game.game_data.submitted_responses.some(
      response => response.player_name === this.player!.name
    );
  }

  getPlayerSubmittedResponse(): string {
    if (!this.game.game_data.submitted_responses || !this.player?.name) {
      return '';
    }

    const submission = this.game.game_data.submitted_responses.find(
      response => response.player_name === this.player!.name
    );

    return submission?.response || '';
  }

  getSubmittedResponsesCount(): number {
    return this.game.game_data.submitted_responses?.length || 0;
  }

  getExpectedResponsesCount(): number {
    // All players except the judge
    return this.game.players.length - 1;
  }

  getResponseProgress(): number {
    const submitted = this.getSubmittedResponsesCount();
    const expected = this.getExpectedResponsesCount();
    return expected > 0 ? submitted / expected : 0;
  }

  isGameComplete(): boolean {
    return this.game.players.some(player => player.score >= this.game.settings.rounds_to_win);
  }

  getGameWinner(): Player | null {
    return this.game.players.find(player => player.score >= this.game.settings.rounds_to_win) || null;
  }

  playAgain() {
    if (!this.player?.is_leader) return;

    this.actionSubmitted.emit({
      type: 'play_again',
      data: {}
    });
  }

  endGame() {
    if (!this.player?.is_leader) return;

    this.actionSubmitted.emit({
      type: 'end_game',
      data: {}
    });
  }
}
