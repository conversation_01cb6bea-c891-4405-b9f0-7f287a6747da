# Jugshine - Godot Client

A Jackbox-style party game with 4 minigames, built in Godot with GDScript.

## Project Structure

```
jugshine/
├── project.godot           # Main Godot project file
├── icon.svg               # Project icon
├── scenes/                # Scene files (.tscn)
│   ├── LoadingScreen.tscn # Initial loading screen
│   ├── MainMenu.tscn      # Main menu with game selection
│   ├── Settings.tscn      # Settings screen
│   └── minigames/         # Minigame scenes
│       ├── PoisoningPigeons.tscn
│       ├── FloridaMan.tscn
│       ├── WashingtonPath.tscn
│       └── PickYourOwnNose.tscn
├── scripts/               # GDScript files (.gd)
│   ├── GameSettings.gd    # Global settings autoload
│   ├── LoadingScreen.gd   # Loading screen logic
│   ├── MainMenu.gd        # Main menu logic
│   ├── Settings.gd        # Settings screen logic
│   ├── BaseMinigame.gd    # Base class for minigames
│   ├── PoisoningPigeons.gd
│   ├── FloridaMan.gd
│   ├── WashingtonPath.gd
│   └── PickYourOwnNose.gd
└── assets/                # Game assets (images, sounds, etc.)
```

## Features Implemented

### Core Systems
- **Global Settings System**: Autoload script managing game settings and navigation
- **Scene Management**: Centralized scene switching through GameSettings
- **Settings Persistence**: NSFW content toggle saved to user://game_settings.cfg

### Navigation
- **Global Navigation**: Escape/Backspace/Controller B returns to main menu
- **Confirmation Dialogs**: Prompts before leaving games (except from Settings)
- **Controller Support**: Full gamepad navigation support

### Scenes
- **Loading Screen**: Server connection with retry mechanism and progress display
- **Main Menu**: 2x2 grid of minigame buttons + centered settings button
- **Settings**: Mature content toggle with immediate save
- **Minigames**: Template scenes ready for game-specific content

### Web Server Integration
- **Connection Check**: HTTP health check during loading
- **Offline Mode**: Graceful fallback if server unavailable
- **Status Display**: Connection status shown in main menu

## Input Mapping

- **return_to_menu**: Escape, Backspace, Controller B
- **ui_accept**: Enter, Space, Controller A
- **ui_cancel**: Escape, Controller B

## Settings

- **NSFW Content**: Boolean toggle for mature content
- **Server URL**: Configurable in GameSettings.gd (default: http://localhost:3000)

## Next Steps

1. Add game-specific logic to minigame scripts
2. Create game assets (images, sounds, UI themes)
3. Implement web server communication for multiplayer features
4. Add webhook handling for real-time game state updates
5. Enhance UI with custom themes and animations

## Development Notes

- All minigames inherit from BaseMinigame class for consistent behavior
- GameSettings autoload provides global access to settings and navigation
- Scene paths are centralized in GameSettings for easy maintenance
- HTTP requests use built-in HTTPRequest node for server communication
