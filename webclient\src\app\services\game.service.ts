import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { io, Socket } from 'socket.io-client';
import { environment } from '../../environments/environment';

export interface Player {
  name: string;
  is_leader: boolean;
  score: number;
  joined_at: string;
  last_seen: string;
  connection_id?: string;
}

export interface GameData {
  // Poisoning Pigeons
  current_prompt?: string;
  player_responses?: { [playerName: string]: string[] };
  submitted_responses?: Array<{
    player_name: string;
    response: string;
    timestamp: string;
  }>;
  current_winner?: {
    player_name: string;
    response: string;
    round: number;
  };

  // Florida Man
  current_scenario?: string;
  story_submissions?: Array<{
    player_name: string;
    story: string;
    timestamp: string;
  }>;
  voting_results?: { [playerName: string]: string };

  // Washington Path
  story_state?: any;
  decision_history?: Array<any>;
  current_decision?: any;

  // Pick Your Own Nose
  current_drawing_prompt?: string;
  drawings?: Array<any>;
  guesses?: { [playerName: string]: string };
}

export interface Game {
  _id?: string;
  room_code: string;
  game_type: 'poisoning_pigeons' | 'florida_man' | 'washington_path' | 'pick_your_nose';
  game_status: string;
  created_at: string;
  updated_at: string;
  settings: {
    max_players: number;
    rounds_to_win: number;
    round_time_limit: number;
    mature_content: boolean;
  };
  current_round: number;
  current_phase: string;
  current_judge?: string;
  players: Player[];
  game_data: GameData;
  rounds: Array<{
    round_number: number;
    winner?: string;
    winning_content?: string;
    completed_at: string;
  }>;
}

@Injectable({
  providedIn: 'root'
})
export class GameService {
  private readonly API_URL = environment.apiUrl;
  private socket: Socket | null = null;
  
  // Observables for reactive state management
  private gameSubject = new BehaviorSubject<Game | null>(null);
  private playerSubject = new BehaviorSubject<Player | null>(null);
  private connectionStatusSubject = new BehaviorSubject<boolean>(false);
  private errorSubject = new BehaviorSubject<string | null>(null);

  public game$ = this.gameSubject.asObservable();
  public player$ = this.playerSubject.asObservable();
  public connectionStatus$ = this.connectionStatusSubject.asObservable();
  public error$ = this.errorSubject.asObservable();

  constructor() {
    this.initializeSocket();
  }

  private initializeSocket(): void {
    this.socket = io(environment.socketUrl);
    
    this.socket.on('connect', () => {
      console.log('Connected to server');
      this.connectionStatusSubject.next(true);
    });

    this.socket.on('disconnect', () => {
      console.log('Disconnected from server');
      this.connectionStatusSubject.next(false);
    });

    this.socket.on('game-updated', (game: Game) => {
      console.log('Game updated:', game);
      this.gameSubject.next(game);
    });

    this.socket.on('player-joined', (data: { player: Player; game: Game }) => {
      console.log('Player joined:', data.player);
      this.gameSubject.next(data.game);
    });

    this.socket.on('player-left', (data: { player: Player; game: Game }) => {
      console.log('Player left:', data.player);
      this.gameSubject.next(data.game);
    });

    this.socket.on('game-closed', (data: { room_code: string }) => {
      console.log('Game closed:', data.room_code);
      this.clearGameState();
    });
  }

  async joinGame(roomCode: string, playerName: string): Promise<boolean> {
    try {
      const response = await fetch(`${this.API_URL}/games/${roomCode}/players`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: playerName,
          connection_id: this.socket?.id
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        this.errorSubject.next(error.error || 'Failed to join game');
        return false;
      }

      const data = await response.json();
      this.gameSubject.next(data.game);
      this.playerSubject.next(data.player);
      
      // Join socket room
      this.socket?.emit('join-game', roomCode);
      
      // Store player info in localStorage for reconnection
      localStorage.setItem('jugshine_player_name', playerName);
      localStorage.setItem('jugshine_room_code', roomCode);
      
      this.errorSubject.next(null);
      return true;

    } catch (error) {
      console.error('Error joining game:', error);
      this.errorSubject.next('Network error. Please try again.');
      return false;
    }
  }

  async leaveGame(): Promise<void> {
    const game = this.gameSubject.value;
    const player = this.playerSubject.value;
    
    if (!game || !player) return;

    try {
      await fetch(`${this.API_URL}/games/${game.room_code}/players/${player.name}`, {
        method: 'DELETE',
      });
    } catch (error) {
      console.error('Error leaving game:', error);
    }

    this.socket?.emit('leave-game', game.room_code);
    this.clearGameState();
  }

  async submitAction(actionType: string, data: any): Promise<boolean> {
    const game = this.gameSubject.value;
    const player = this.playerSubject.value;
    
    if (!game || !player) {
      this.errorSubject.next('No active game or player');
      return false;
    }

    try {
      const response = await fetch(`${this.API_URL}/players/${game.room_code}/${player.name}/actions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action_type: actionType,
          data: data
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        this.errorSubject.next(error.error || 'Failed to submit action');
        return false;
      }

      const result = await response.json();
      this.gameSubject.next(result.game);
      this.errorSubject.next(null);
      return true;

    } catch (error) {
      console.error('Error submitting action:', error);
      this.errorSubject.next('Network error. Please try again.');
      return false;
    }
  }

  async reconnectToGame(): Promise<boolean> {
    const savedPlayerName = localStorage.getItem('jugshine_player_name');
    const savedRoomCode = localStorage.getItem('jugshine_room_code');
    
    if (!savedPlayerName || !savedRoomCode) {
      return false;
    }

    try {
      // First check if game exists
      const response = await fetch(`${this.API_URL}/games/${savedRoomCode}`);
      
      if (!response.ok) {
        this.clearStoredGameInfo();
        return false;
      }

      const game: Game = await response.json();
      
      // Check if player is still in the game
      const player = game.players.find(p => p.name === savedPlayerName);
      
      if (!player) {
        this.clearStoredGameInfo();
        return false;
      }

      // Reconnect
      this.gameSubject.next(game);
      this.playerSubject.next(player);
      this.socket?.emit('join-game', savedRoomCode);
      
      // Update connection ID
      await fetch(`${this.API_URL}/players/${savedRoomCode}/${savedPlayerName}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          connection_id: this.socket?.id
        }),
      });

      return true;

    } catch (error) {
      console.error('Error reconnecting to game:', error);
      this.clearStoredGameInfo();
      return false;
    }
  }

  private clearGameState(): void {
    this.gameSubject.next(null);
    this.playerSubject.next(null);
    this.clearStoredGameInfo();
  }

  private clearStoredGameInfo(): void {
    localStorage.removeItem('jugshine_player_name');
    localStorage.removeItem('jugshine_room_code');
  }

  getCurrentGame(): Game | null {
    return this.gameSubject.value;
  }

  getCurrentPlayer(): Player | null {
    return this.playerSubject.value;
  }

  isPlayerLeader(): boolean {
    const player = this.playerSubject.value;
    return player?.is_leader || false;
  }

  isPlayerJudge(): boolean {
    const game = this.gameSubject.value;
    const player = this.playerSubject.value;
    return game?.current_judge === player?.name;
  }

  getPlayerResponses(): string[] {
    const game = this.gameSubject.value;
    const player = this.playerSubject.value;
    
    if (!game || !player || !game.game_data.player_responses) {
      return [];
    }

    return game.game_data.player_responses[player.name] || [];
  }
}
