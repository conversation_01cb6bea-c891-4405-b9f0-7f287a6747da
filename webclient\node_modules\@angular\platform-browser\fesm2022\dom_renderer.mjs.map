{"version": 3, "file": "dom_renderer.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/platform-browser/src/dom/events/event_manager.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/platform-browser/src/dom/shared_styles_host.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/platform-browser/src/dom/dom_renderer.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Inject,\n  Injectable,\n  InjectionToken,\n  NgZone,\n  ɵRuntimeError as RuntimeError,\n  type ListenerOptions,\n} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../../errors';\n\n/**\n * The injection token for plugins of the `EventManager` service.\n *\n * @publicApi\n */\nexport const EVENT_MANAGER_PLUGINS = new InjectionToken<EventManagerPlugin[]>(\n  ngDevMode ? 'EventManagerPlugins' : '',\n);\n\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\n@Injectable()\nexport class EventManager {\n  private _plugins: EventManagerPlugin[];\n  private _eventNameToPlugin = new Map<string, EventManagerPlugin>();\n\n  /**\n   * Initializes an instance of the event-manager service.\n   */\n  constructor(\n    @Inject(EVENT_MANAGER_PLUGINS) plugins: EventManagerPlugin[],\n    private _zone: Ng<PERSON>one,\n  ) {\n    plugins.forEach((plugin) => {\n      plugin.manager = this;\n    });\n    this._plugins = plugins.slice().reverse();\n  }\n\n  /**\n   * Registers a handler for a specific element and event.\n   *\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @param options Options that configure how the event listener is bound.\n   * @returns  A callback function that can be used to remove the handler.\n   */\n  addEventListener(\n    element: HTMLElement,\n    eventName: string,\n    handler: Function,\n    options?: ListenerOptions,\n  ): Function {\n    const plugin = this._findPluginFor(eventName);\n    return plugin.addEventListener(element, eventName, handler, options);\n  }\n\n  /**\n   * Retrieves the compilation zone in which event listeners are registered.\n   */\n  getZone(): NgZone {\n    return this._zone;\n  }\n\n  /** @internal */\n  _findPluginFor(eventName: string): EventManagerPlugin {\n    let plugin = this._eventNameToPlugin.get(eventName);\n    if (plugin) {\n      return plugin;\n    }\n\n    const plugins = this._plugins;\n    plugin = plugins.find((plugin) => plugin.supports(eventName));\n    if (!plugin) {\n      throw new RuntimeError(\n        RuntimeErrorCode.NO_PLUGIN_FOR_EVENT,\n        (typeof ngDevMode === 'undefined' || ngDevMode) &&\n          `No event manager plugin found for event ${eventName}`,\n      );\n    }\n\n    this._eventNameToPlugin.set(eventName, plugin);\n    return plugin;\n  }\n}\n\n/**\n * The plugin definition for the `EventManager` class\n *\n * It can be used as a base class to create custom manager plugins, i.e. you can create your own\n * class that extends the `EventManagerPlugin` one.\n *\n * @publicApi\n */\nexport abstract class EventManagerPlugin {\n  // TODO: remove (has some usage in G3)\n  constructor(private _doc: any) {}\n\n  // Using non-null assertion because it's set by EventManager's constructor\n  manager!: EventManager;\n\n  /**\n   * Should return `true` for every event name that should be supported by this plugin\n   */\n  abstract supports(eventName: string): boolean;\n\n  /**\n   * Implement the behaviour for the supported events\n   */\n  abstract addEventListener(\n    element: HTMLElement,\n    eventName: string,\n    handler: Function,\n    options?: ListenerOptions,\n  ): Function;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT, isPlatformServer} from '@angular/common';\nimport {\n  APP_ID,\n  CSP_NONCE,\n  Inject,\n  Injectable,\n  OnDestroy,\n  Optional,\n  PLATFORM_ID,\n} from '@angular/core';\n\n/** The style elements attribute name used to set value of `APP_ID` token. */\nconst APP_ID_ATTRIBUTE_NAME = 'ng-app-id';\n\n/**\n * A record of usage for a specific style including all elements added to the DOM\n * that contain a given style.\n */\ninterface UsageRecord<T> {\n  elements: T[];\n  usage: number;\n}\n\n/**\n * Removes all provided elements from the document.\n * @param elements An array of HTML Elements.\n */\nfunction removeElements(elements: Iterable<HTMLElement>): void {\n  for (const element of elements) {\n    element.remove();\n  }\n}\n\n/**\n * Creates a `style` element with the provided inline style content.\n * @param style A string of the inline style content.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLStyleElement instance.\n */\nfunction createStyleElement(style: string, doc: Document): HTMLStyleElement {\n  const styleElement = doc.createElement('style');\n  styleElement.textContent = style;\n\n  return styleElement;\n}\n\n/**\n * Searches a DOM document's head element for style elements with a matching application\n * identifier attribute (`ng-app-id`) to the provide identifier and adds usage records for each.\n * @param doc An HTML DOM document instance.\n * @param appId A string containing an Angular application identifer.\n * @param inline A Map object for tracking inline (defined via `styles` in component decorator) style usage.\n * @param external A Map object for tracking external (defined via `styleUrls` in component decorator) style usage.\n */\nfunction addServerStyles(\n  doc: Document,\n  appId: string,\n  inline: Map<string, UsageRecord<HTMLStyleElement>>,\n  external: Map<string, UsageRecord<HTMLLinkElement>>,\n): void {\n  const elements = doc.head?.querySelectorAll<HTMLStyleElement | HTMLLinkElement>(\n    `style[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"],link[${APP_ID_ATTRIBUTE_NAME}=\"${appId}\"]`,\n  );\n\n  if (elements) {\n    for (const styleElement of elements) {\n      styleElement.removeAttribute(APP_ID_ATTRIBUTE_NAME);\n      if (styleElement instanceof HTMLLinkElement) {\n        // Only use filename from href\n        // The href is build time generated with a unique value to prevent duplicates.\n        external.set(styleElement.href.slice(styleElement.href.lastIndexOf('/') + 1), {\n          usage: 0,\n          elements: [styleElement],\n        });\n      } else if (styleElement.textContent) {\n        inline.set(styleElement.textContent, {usage: 0, elements: [styleElement]});\n      }\n    }\n  }\n}\n\n/**\n * Creates a `link` element for the provided external style URL.\n * @param url A string of the URL for the stylesheet.\n * @param doc A DOM Document to use to create the element.\n * @returns An HTMLLinkElement instance.\n */\nexport function createLinkElement(url: string, doc: Document): HTMLLinkElement {\n  const linkElement = doc.createElement('link');\n  linkElement.setAttribute('rel', 'stylesheet');\n  linkElement.setAttribute('href', url);\n\n  return linkElement;\n}\n\n@Injectable()\nexport class SharedStylesHost implements OnDestroy {\n  /**\n   * Provides usage information for active inline style content and associated HTML <style> elements.\n   * Embedded styles typically originate from the `styles` metadata of a rendered component.\n   */\n  private readonly inline = new Map<string /** content */, UsageRecord<HTMLStyleElement>>();\n\n  /**\n   * Provides usage information for active external style URLs and the associated HTML <link> elements.\n   * External styles typically originate from the `ɵɵExternalStylesFeature` of a rendered component.\n   */\n  private readonly external = new Map<string /** URL */, UsageRecord<HTMLLinkElement>>();\n\n  /**\n   * Set of host DOM nodes that will have styles attached.\n   */\n  private readonly hosts = new Set<Node>();\n\n  /**\n   * Whether the application code is currently executing on a server.\n   */\n  private readonly isServer: boolean;\n\n  constructor(\n    @Inject(DOCUMENT) private readonly doc: Document,\n    @Inject(APP_ID) private readonly appId: string,\n    @Inject(CSP_NONCE) @Optional() private readonly nonce?: string | null,\n    @Inject(PLATFORM_ID) platformId: object = {},\n  ) {\n    this.isServer = isPlatformServer(platformId);\n    addServerStyles(doc, appId, this.inline, this.external);\n    this.hosts.add(doc.head);\n  }\n\n  /**\n   * Adds embedded styles to the DOM via HTML `style` elements.\n   * @param styles An array of style content strings.\n   */\n  addStyles(styles: string[], urls?: string[]): void {\n    for (const value of styles) {\n      this.addUsage(value, this.inline, createStyleElement);\n    }\n\n    urls?.forEach((value) => this.addUsage(value, this.external, createLinkElement));\n  }\n\n  /**\n   * Removes embedded styles from the DOM that were added as HTML `style` elements.\n   * @param styles An array of style content strings.\n   */\n  removeStyles(styles: string[], urls?: string[]): void {\n    for (const value of styles) {\n      this.removeUsage(value, this.inline);\n    }\n\n    urls?.forEach((value) => this.removeUsage(value, this.external));\n  }\n\n  protected addUsage<T extends HTMLElement>(\n    value: string,\n    usages: Map<string, UsageRecord<T>>,\n    creator: (value: string, doc: Document) => T,\n  ): void {\n    // Attempt to get any current usage of the value\n    const record = usages.get(value);\n\n    // If existing, just increment the usage count\n    if (record) {\n      if ((typeof ngDevMode === 'undefined' || ngDevMode) && record.usage === 0) {\n        // A usage count of zero indicates a preexisting server generated style.\n        // This attribute is solely used for debugging purposes of SSR style reuse.\n        record.elements.forEach((element) => element.setAttribute('ng-style-reused', ''));\n      }\n      record.usage++;\n    } else {\n      // Otherwise, create an entry to track the elements and add element for each host\n      usages.set(value, {\n        usage: 1,\n        elements: [...this.hosts].map((host) => this.addElement(host, creator(value, this.doc))),\n      });\n    }\n  }\n\n  protected removeUsage<T extends HTMLElement>(\n    value: string,\n    usages: Map<string, UsageRecord<T>>,\n  ): void {\n    // Attempt to get any current usage of the value\n    const record = usages.get(value);\n\n    // If there is a record, reduce the usage count and if no longer used,\n    // remove from DOM and delete usage record.\n    if (record) {\n      record.usage--;\n      if (record.usage <= 0) {\n        removeElements(record.elements);\n        usages.delete(value);\n      }\n    }\n  }\n\n  ngOnDestroy(): void {\n    for (const [, {elements}] of [...this.inline, ...this.external]) {\n      removeElements(elements);\n    }\n    this.hosts.clear();\n  }\n\n  /**\n   * Adds a host node to the set of style hosts and adds all existing style usage to\n   * the newly added host node.\n   *\n   * This is currently only used for Shadow DOM encapsulation mode.\n   */\n  addHost(hostNode: Node): void {\n    this.hosts.add(hostNode);\n\n    // Add existing styles to new host\n    for (const [style, {elements}] of this.inline) {\n      elements.push(this.addElement(hostNode, createStyleElement(style, this.doc)));\n    }\n    for (const [url, {elements}] of this.external) {\n      elements.push(this.addElement(hostNode, createLinkElement(url, this.doc)));\n    }\n  }\n\n  removeHost(hostNode: Node): void {\n    this.hosts.delete(hostNode);\n  }\n\n  private addElement<T extends HTMLElement>(host: Node, element: T): T {\n    // Add a nonce if present\n    if (this.nonce) {\n      element.setAttribute('nonce', this.nonce);\n    }\n\n    // Add application identifier when on the server to support client-side reuse\n    if (this.isServer) {\n      element.setAttribute(APP_ID_ATTRIBUTE_NAME, this.appId);\n    }\n\n    // Insert the element into the DOM with the host node as parent\n    return host.appendChild(element);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DOCUMENT, ɵgetDOM as getDOM} from '@angular/common';\nimport {\n  APP_ID,\n  CSP_NONCE,\n  Inject,\n  Injectable,\n  InjectionToken,\n  NgZone,\n  OnDestroy,\n  PLATFORM_ID,\n  Renderer2,\n  RendererFactory2,\n  RendererStyleFlags2,\n  RendererType2,\n  ViewEncapsulation,\n  ɵRuntimeError as RuntimeError,\n  type ListenerOptions,\n  ɵTracingService as TracingService,\n  ɵTracingSnapshot as TracingSnapshot,\n  Optional,\n} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../errors';\n\nimport {EventManager} from './events/event_manager';\nimport {createLinkElement, SharedStylesHost} from './shared_styles_host';\n\nexport const NAMESPACE_URIS: {[ns: string]: string} = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/',\n  'math': 'http://www.w3.org/1998/Math/MathML',\n};\n\nconst COMPONENT_REGEX = /%COMP%/g;\nconst SOURCEMAP_URL_REGEXP = /\\/\\*#\\s*sourceMappingURL=(.+?)\\s*\\*\\//;\nconst PROTOCOL_REGEXP = /^https?:/;\n\nexport const COMPONENT_VARIABLE = '%COMP%';\nexport const HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nexport const CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = true;\n\n/**\n * A DI token that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `true`.\n * @publicApi\n */\nexport const REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken<boolean>(\n  ngDevMode ? 'RemoveStylesOnCompDestroy' : '',\n  {\n    providedIn: 'root',\n    factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT,\n  },\n);\n\nexport function shimContentAttribute(componentShortId: string): string {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\n\nexport function shimHostAttribute(componentShortId: string): string {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\n\nexport function shimStylesContent(compId: string, styles: string[]): string[] {\n  return styles.map((s) => s.replace(COMPONENT_REGEX, compId));\n}\n\n/**\n * Prepends a baseHref to the `sourceMappingURL` within the provided CSS content.\n * If the `sourceMappingURL` contains an inline (encoded) map, the function skips processing.\n *\n * @note For inline stylesheets, the `sourceMappingURL` is relative to the page's origin\n * and not the provided baseHref. This function is needed as when accessing the page with a URL\n * containing two or more segments.\n * For example, if the baseHref is set to `/`, and you visit a URL like `http://localhost/foo/bar`,\n * the map would be requested from `http://localhost/foo/bar/comp.css.map` instead of what you'd expect,\n * which is `http://localhost/comp.css.map`. This behavior is corrected by modifying the `sourceMappingURL`\n * to ensure external source maps are loaded relative to the baseHref.\n *\n\n * @param baseHref - The base URL to prepend to the `sourceMappingURL`.\n * @param styles - An array of CSS content strings, each potentially containing a `sourceMappingURL`.\n * @returns The updated array of CSS content strings with modified `sourceMappingURL` values,\n * or the original content if no modification is needed.\n */\nexport function addBaseHrefToCssSourceMap(baseHref: string, styles: string[]): string[] {\n  if (!baseHref) {\n    return styles;\n  }\n\n  const absoluteBaseHrefUrl = new URL(baseHref, 'http://localhost');\n\n  return styles.map((cssContent) => {\n    if (!cssContent.includes('sourceMappingURL=')) {\n      return cssContent;\n    }\n\n    return cssContent.replace(SOURCEMAP_URL_REGEXP, (_, sourceMapUrl) => {\n      if (\n        sourceMapUrl[0] === '/' ||\n        sourceMapUrl.startsWith('data:') ||\n        PROTOCOL_REGEXP.test(sourceMapUrl)\n      ) {\n        return `/*# sourceMappingURL=${sourceMapUrl} */`;\n      }\n\n      const {pathname: resolvedSourceMapUrl} = new URL(sourceMapUrl, absoluteBaseHrefUrl);\n\n      return `/*# sourceMappingURL=${resolvedSourceMapUrl} */`;\n    });\n  });\n}\n\n@Injectable()\nexport class DomRendererFactory2 implements RendererFactory2, OnDestroy {\n  private readonly rendererByCompId = new Map<\n    string,\n    EmulatedEncapsulationDomRenderer2 | NoneEncapsulationDomRenderer\n  >();\n  private readonly defaultRenderer: Renderer2;\n  private readonly platformIsServer: boolean;\n\n  constructor(\n    private readonly eventManager: EventManager,\n    private readonly sharedStylesHost: SharedStylesHost,\n    @Inject(APP_ID) private readonly appId: string,\n    @Inject(REMOVE_STYLES_ON_COMPONENT_DESTROY) private removeStylesOnCompDestroy: boolean,\n    @Inject(DOCUMENT) private readonly doc: Document,\n    @Inject(PLATFORM_ID) readonly platformId: Object,\n    readonly ngZone: NgZone,\n    @Inject(CSP_NONCE) private readonly nonce: string | null = null,\n    @Inject(TracingService)\n    @Optional()\n    private readonly tracingService: TracingService<TracingSnapshot> | null = null,\n  ) {\n    this.platformIsServer = typeof ngServerMode !== 'undefined' && ngServerMode;\n    this.defaultRenderer = new DefaultDomRenderer2(\n      eventManager,\n      doc,\n      ngZone,\n      this.platformIsServer,\n      this.tracingService,\n    );\n  }\n\n  createRenderer(element: any, type: RendererType2 | null): Renderer2 {\n    if (!element || !type) {\n      return this.defaultRenderer;\n    }\n\n    if (\n      typeof ngServerMode !== 'undefined' &&\n      ngServerMode &&\n      type.encapsulation === ViewEncapsulation.ShadowDom\n    ) {\n      // Domino does not support shadow DOM.\n      type = {...type, encapsulation: ViewEncapsulation.Emulated};\n    }\n\n    const renderer = this.getOrCreateRenderer(element, type);\n    // Renderers have different logic due to different encapsulation behaviours.\n    // Ex: for emulated, an attribute is added to the element.\n    if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n      renderer.applyToHost(element);\n    } else if (renderer instanceof NoneEncapsulationDomRenderer) {\n      renderer.applyStyles();\n    }\n\n    return renderer;\n  }\n\n  private getOrCreateRenderer(element: any, type: RendererType2): Renderer2 {\n    const rendererByCompId = this.rendererByCompId;\n    let renderer = rendererByCompId.get(type.id);\n\n    if (!renderer) {\n      const doc = this.doc;\n      const ngZone = this.ngZone;\n      const eventManager = this.eventManager;\n      const sharedStylesHost = this.sharedStylesHost;\n      const removeStylesOnCompDestroy = this.removeStylesOnCompDestroy;\n      const platformIsServer = this.platformIsServer;\n      const tracingService = this.tracingService;\n\n      switch (type.encapsulation) {\n        case ViewEncapsulation.Emulated:\n          renderer = new EmulatedEncapsulationDomRenderer2(\n            eventManager,\n            sharedStylesHost,\n            type,\n            this.appId,\n            removeStylesOnCompDestroy,\n            doc,\n            ngZone,\n            platformIsServer,\n            tracingService,\n          );\n          break;\n        case ViewEncapsulation.ShadowDom:\n          return new ShadowDomRenderer(\n            eventManager,\n            sharedStylesHost,\n            element,\n            type,\n            doc,\n            ngZone,\n            this.nonce,\n            platformIsServer,\n            tracingService,\n          );\n        default:\n          renderer = new NoneEncapsulationDomRenderer(\n            eventManager,\n            sharedStylesHost,\n            type,\n            removeStylesOnCompDestroy,\n            doc,\n            ngZone,\n            platformIsServer,\n            tracingService,\n          );\n          break;\n      }\n\n      rendererByCompId.set(type.id, renderer);\n    }\n\n    return renderer;\n  }\n\n  ngOnDestroy() {\n    this.rendererByCompId.clear();\n  }\n\n  /**\n   * Used during HMR to clear any cached data about a component.\n   * @param componentId ID of the component that is being replaced.\n   */\n  protected componentReplaced(componentId: string) {\n    this.rendererByCompId.delete(componentId);\n  }\n}\n\nclass DefaultDomRenderer2 implements Renderer2 {\n  data: {[key: string]: any} = Object.create(null);\n\n  /**\n   * By default this renderer throws when encountering synthetic properties\n   * This can be disabled for example by the AsyncAnimationRendererFactory\n   */\n  throwOnSyntheticProps = true;\n\n  constructor(\n    private readonly eventManager: EventManager,\n    private readonly doc: Document,\n    private readonly ngZone: NgZone,\n    private readonly platformIsServer: boolean,\n    private readonly tracingService: TracingService<TracingSnapshot> | null,\n  ) {}\n\n  destroy(): void {}\n\n  destroyNode = null;\n\n  createElement(name: string, namespace?: string): any {\n    if (namespace) {\n      // TODO: `|| namespace` was added in\n      // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n      // support how Ivy passed around the namespace URI rather than short name at the time. It did\n      // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n      // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n      // namespaces should be and make it consistent.\n      // Related issues:\n      // https://github.com/angular/angular/issues/44028\n      // https://github.com/angular/angular/issues/44883\n      return this.doc.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n    }\n\n    return this.doc.createElement(name);\n  }\n\n  createComment(value: string): any {\n    return this.doc.createComment(value);\n  }\n\n  createText(value: string): any {\n    return this.doc.createTextNode(value);\n  }\n\n  appendChild(parent: any, newChild: any): void {\n    const targetParent = isTemplateNode(parent) ? parent.content : parent;\n    targetParent.appendChild(newChild);\n  }\n\n  insertBefore(parent: any, newChild: any, refChild: any): void {\n    if (parent) {\n      const targetParent = isTemplateNode(parent) ? parent.content : parent;\n      targetParent.insertBefore(newChild, refChild);\n    }\n  }\n\n  removeChild(_parent: any, oldChild: any): void {\n    oldChild.remove();\n  }\n\n  selectRootElement(selectorOrNode: string | any, preserveContent?: boolean): any {\n    let el: any =\n      typeof selectorOrNode === 'string' ? this.doc.querySelector(selectorOrNode) : selectorOrNode;\n    if (!el) {\n      throw new RuntimeError(\n        RuntimeErrorCode.ROOT_NODE_NOT_FOUND,\n        (typeof ngDevMode === 'undefined' || ngDevMode) &&\n          `The selector \"${selectorOrNode}\" did not match any elements`,\n      );\n    }\n    if (!preserveContent) {\n      el.textContent = '';\n    }\n    return el;\n  }\n\n  parentNode(node: any): any {\n    return node.parentNode;\n  }\n\n  nextSibling(node: any): any {\n    return node.nextSibling;\n  }\n\n  setAttribute(el: any, name: string, value: string, namespace?: string): void {\n    if (namespace) {\n      name = namespace + ':' + name;\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n\n  removeAttribute(el: any, name: string, namespace?: string): void {\n    if (namespace) {\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n\n  addClass(el: any, name: string): void {\n    el.classList.add(name);\n  }\n\n  removeClass(el: any, name: string): void {\n    el.classList.remove(name);\n  }\n\n  setStyle(el: any, style: string, value: any, flags: RendererStyleFlags2): void {\n    if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n      el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n\n  removeStyle(el: any, style: string, flags: RendererStyleFlags2): void {\n    if (flags & RendererStyleFlags2.DashCase) {\n      // removeProperty has no effect when used on camelCased properties.\n      el.style.removeProperty(style);\n    } else {\n      el.style[style] = '';\n    }\n  }\n\n  setProperty(el: any, name: string, value: any): void {\n    if (el == null) {\n      return;\n    }\n\n    (typeof ngDevMode === 'undefined' || ngDevMode) &&\n      this.throwOnSyntheticProps &&\n      checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n\n  setValue(node: any, value: string): void {\n    node.nodeValue = value;\n  }\n\n  listen(\n    target: 'window' | 'document' | 'body' | any,\n    event: string,\n    callback: (event: any) => boolean,\n    options?: ListenerOptions,\n  ): () => void {\n    (typeof ngDevMode === 'undefined' || ngDevMode) &&\n      this.throwOnSyntheticProps &&\n      checkNoSyntheticProp(event, 'listener');\n    if (typeof target === 'string') {\n      target = getDOM().getGlobalEventTarget(this.doc, target);\n      if (!target) {\n        throw new RuntimeError(\n          RuntimeErrorCode.UNSUPPORTED_EVENT_TARGET,\n          (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            `Unsupported event target ${target} for event ${event}`,\n        );\n      }\n    }\n\n    let wrappedCallback = this.decoratePreventDefault(callback);\n\n    if (this.tracingService?.wrapEventListener) {\n      wrappedCallback = this.tracingService.wrapEventListener(target, event, wrappedCallback);\n    }\n\n    return this.eventManager.addEventListener(\n      target,\n      event,\n      wrappedCallback,\n      options,\n    ) as VoidFunction;\n  }\n\n  private decoratePreventDefault(eventHandler: Function): Function {\n    // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n    // decoratePreventDefault or is a listener added outside the Angular context so it can handle\n    // the two differently. In the first case, the special '__ngUnwrap__' token is passed to the\n    // unwrap the listener (see below).\n    return (event: any) => {\n      // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n      // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The\n      // debug_node can inspect the listener toString contents for the existence of this special\n      // token. Because the token is a string literal, it is ensured to not be modified by compiled\n      // code.\n      if (event === '__ngUnwrap__') {\n        return eventHandler;\n      }\n\n      // Run the event handler inside the ngZone because event handlers are not patched\n      // by Zone on the server. This is required only for tests.\n      const allowDefaultBehavior =\n        typeof ngServerMode !== 'undefined' && ngServerMode\n          ? this.ngZone.runGuarded(() => eventHandler(event))\n          : eventHandler(event);\n      if (allowDefaultBehavior === false) {\n        event.preventDefault();\n      }\n\n      return undefined;\n    };\n  }\n}\n\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name: string, nameKind: string) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new RuntimeError(\n      RuntimeErrorCode.UNEXPECTED_SYNTHETIC_PROPERTY,\n      `Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Make sure \\`provideAnimationsAsync()\\`, \\`provideAnimations()\\` or \\`provideNoopAnimations()\\` call was added to a list of providers used to bootstrap an application.\n  - There is a corresponding animation configuration named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.dev/api/core/Component#animations).`,\n    );\n  }\n}\n\nfunction isTemplateNode(node: any): node is HTMLTemplateElement {\n  return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\n\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n  private shadowRoot: any;\n\n  constructor(\n    eventManager: EventManager,\n    private sharedStylesHost: SharedStylesHost,\n    private hostEl: any,\n    component: RendererType2,\n    doc: Document,\n    ngZone: NgZone,\n    nonce: string | null,\n    platformIsServer: boolean,\n    tracingService: TracingService<TracingSnapshot> | null,\n  ) {\n    super(eventManager, doc, ngZone, platformIsServer, tracingService);\n    this.shadowRoot = (hostEl as any).attachShadow({mode: 'open'});\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    let styles = component.styles;\n    if (ngDevMode) {\n      // We only do this in development, as for production users should not add CSS sourcemaps to components.\n      const baseHref = getDOM().getBaseHref(doc) ?? '';\n      styles = addBaseHrefToCssSourceMap(baseHref, styles);\n    }\n\n    styles = shimStylesContent(component.id, styles);\n\n    for (const style of styles) {\n      const styleEl = document.createElement('style');\n\n      if (nonce) {\n        styleEl.setAttribute('nonce', nonce);\n      }\n\n      styleEl.textContent = style;\n      this.shadowRoot.appendChild(styleEl);\n    }\n\n    // Apply any external component styles to the shadow root for the component's element.\n    // The ShadowDOM renderer uses an alternative execution path for component styles that\n    // does not use the SharedStylesHost that other encapsulation modes leverage. Much like\n    // the manual addition of embedded styles directly above, any external stylesheets\n    // must be manually added here to ensure ShadowDOM components are correctly styled.\n    // TODO: Consider reworking the DOM Renderers to consolidate style handling.\n    const styleUrls = component.getExternalStyles?.();\n    if (styleUrls) {\n      for (const styleUrl of styleUrls) {\n        const linkEl = createLinkElement(styleUrl, doc);\n        if (nonce) {\n          linkEl.setAttribute('nonce', nonce);\n        }\n        this.shadowRoot.appendChild(linkEl);\n      }\n    }\n  }\n\n  private nodeOrShadowRoot(node: any): any {\n    return node === this.hostEl ? this.shadowRoot : node;\n  }\n\n  override appendChild(parent: any, newChild: any): void {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n  override insertBefore(parent: any, newChild: any, refChild: any): void {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n  override removeChild(_parent: any, oldChild: any): void {\n    return super.removeChild(null, oldChild);\n  }\n  override parentNode(node: any): any {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n\n  override destroy() {\n    this.sharedStylesHost.removeHost(this.shadowRoot);\n  }\n}\n\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n  private readonly styles: string[];\n  private readonly styleUrls?: string[];\n\n  constructor(\n    eventManager: EventManager,\n    private readonly sharedStylesHost: SharedStylesHost,\n    component: RendererType2,\n    private removeStylesOnCompDestroy: boolean,\n    doc: Document,\n    ngZone: NgZone,\n    platformIsServer: boolean,\n    tracingService: TracingService<TracingSnapshot> | null,\n    compId?: string,\n  ) {\n    super(eventManager, doc, ngZone, platformIsServer, tracingService);\n    let styles = component.styles;\n    if (ngDevMode) {\n      // We only do this in development, as for production users should not add CSS sourcemaps to components.\n      const baseHref = getDOM().getBaseHref(doc) ?? '';\n      styles = addBaseHrefToCssSourceMap(baseHref, styles);\n    }\n\n    this.styles = compId ? shimStylesContent(compId, styles) : styles;\n    this.styleUrls = component.getExternalStyles?.(compId);\n  }\n\n  applyStyles(): void {\n    this.sharedStylesHost.addStyles(this.styles, this.styleUrls);\n  }\n\n  override destroy(): void {\n    if (!this.removeStylesOnCompDestroy) {\n      return;\n    }\n\n    this.sharedStylesHost.removeStyles(this.styles, this.styleUrls);\n  }\n}\n\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n  private contentAttr: string;\n  private hostAttr: string;\n\n  constructor(\n    eventManager: EventManager,\n    sharedStylesHost: SharedStylesHost,\n    component: RendererType2,\n    appId: string,\n    removeStylesOnCompDestroy: boolean,\n    doc: Document,\n    ngZone: NgZone,\n    platformIsServer: boolean,\n    tracingService: TracingService<TracingSnapshot> | null,\n  ) {\n    const compId = appId + '-' + component.id;\n    super(\n      eventManager,\n      sharedStylesHost,\n      component,\n      removeStylesOnCompDestroy,\n      doc,\n      ngZone,\n      platformIsServer,\n      tracingService,\n      compId,\n    );\n    this.contentAttr = shimContentAttribute(compId);\n    this.hostAttr = shimHostAttribute(compId);\n  }\n\n  applyToHost(element: any): void {\n    this.applyStyles();\n    this.setAttribute(element, this.hostAttr, '');\n  }\n\n  override createElement(parent: any, name: string): Element {\n    const el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n}\n"], "names": ["RuntimeError", "i1.EventManager", "i2.SharedStylesHost", "TracingService", "getDOM"], "mappings": ";;;;;;;;;;AAmBA;;;;AAIG;AACU,MAAA,qBAAqB,GAAG,IAAI,cAAc,CACrD,SAAS,GAAG,qBAAqB,GAAG,EAAE;AAGxC;;;;;AAKG;MAEU,YAAY,CAAA;AASb,IAAA,KAAA;AARF,IAAA,QAAQ;AACR,IAAA,kBAAkB,GAAG,IAAI,GAAG,EAA8B;AAElE;;AAEG;IACH,WACiC,CAAA,OAA6B,EACpD,KAAa,EAAA;QAAb,IAAK,CAAA,KAAA,GAAL,KAAK;AAEb,QAAA,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AACzB,YAAA,MAAM,CAAC,OAAO,GAAG,IAAI;AACvB,SAAC,CAAC;QACF,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE;;AAG3C;;;;;;;;;AASG;AACH,IAAA,gBAAgB,CACd,OAAoB,EACpB,SAAiB,EACjB,OAAiB,EACjB,OAAyB,EAAA;QAEzB,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC;AAC7C,QAAA,OAAO,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC;;AAGtE;;AAEG;IACH,OAAO,GAAA;QACL,OAAO,IAAI,CAAC,KAAK;;;AAInB,IAAA,cAAc,CAAC,SAAiB,EAAA;QAC9B,IAAI,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC;QACnD,IAAI,MAAM,EAAE;AACV,YAAA,OAAO,MAAM;;AAGf,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ;AAC7B,QAAA,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC7D,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAIA,aAAY,CAAA,IAAA,6CAEpB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;gBAC5C,CAA2C,wCAAA,EAAA,SAAS,CAAE,CAAA,CACzD;;QAGH,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC;AAC9C,QAAA,OAAO,MAAM;;AA9DJ,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,kBAQb,qBAAqB,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHARpB,YAAY,EAAA,CAAA;;sGAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBADxB;;0BASI,MAAM;2BAAC,qBAAqB;;AA0DjC;;;;;;;AAOG;MACmB,kBAAkB,CAAA;AAElB,IAAA,IAAA;;AAApB,IAAA,WAAA,CAAoB,IAAS,EAAA;QAAT,IAAI,CAAA,IAAA,GAAJ,IAAI;;;AAGxB,IAAA,OAAO;AAgBR;;AC/GD;AACA,MAAM,qBAAqB,GAAG,WAAW;AAWzC;;;AAGG;AACH,SAAS,cAAc,CAAC,QAA+B,EAAA;AACrD,IAAA,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;QAC9B,OAAO,CAAC,MAAM,EAAE;;AAEpB;AAEA;;;;;AAKG;AACH,SAAS,kBAAkB,CAAC,KAAa,EAAE,GAAa,EAAA;IACtD,MAAM,YAAY,GAAG,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC;AAC/C,IAAA,YAAY,CAAC,WAAW,GAAG,KAAK;AAEhC,IAAA,OAAO,YAAY;AACrB;AAEA;;;;;;;AAOG;AACH,SAAS,eAAe,CACtB,GAAa,EACb,KAAa,EACb,MAAkD,EAClD,QAAmD,EAAA;AAEnD,IAAA,MAAM,QAAQ,GAAG,GAAG,CAAC,IAAI,EAAE,gBAAgB,CACzC,CAAA,MAAA,EAAS,qBAAqB,CAAA,EAAA,EAAK,KAAK,CAAW,QAAA,EAAA,qBAAqB,KAAK,KAAK,CAAA,EAAA,CAAI,CACvF;IAED,IAAI,QAAQ,EAAE;AACZ,QAAA,KAAK,MAAM,YAAY,IAAI,QAAQ,EAAE;AACnC,YAAA,YAAY,CAAC,eAAe,CAAC,qBAAqB,CAAC;AACnD,YAAA,IAAI,YAAY,YAAY,eAAe,EAAE;;;gBAG3C,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;AAC5E,oBAAA,KAAK,EAAE,CAAC;oBACR,QAAQ,EAAE,CAAC,YAAY,CAAC;AACzB,iBAAA,CAAC;;AACG,iBAAA,IAAI,YAAY,CAAC,WAAW,EAAE;AACnC,gBAAA,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,WAAW,EAAE,EAAC,KAAK,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,YAAY,CAAC,EAAC,CAAC;;;;AAIlF;AAEA;;;;;AAKG;AACa,SAAA,iBAAiB,CAAC,GAAW,EAAE,GAAa,EAAA;IAC1D,MAAM,WAAW,GAAG,GAAG,CAAC,aAAa,CAAC,MAAM,CAAC;AAC7C,IAAA,WAAW,CAAC,YAAY,CAAC,KAAK,EAAE,YAAY,CAAC;AAC7C,IAAA,WAAW,CAAC,YAAY,CAAC,MAAM,EAAE,GAAG,CAAC;AAErC,IAAA,OAAO,WAAW;AACpB;MAGa,gBAAgB,CAAA;AAwBU,IAAA,GAAA;AACF,IAAA,KAAA;AACe,IAAA,KAAA;AAzBlD;;;AAGG;AACc,IAAA,MAAM,GAAG,IAAI,GAAG,EAAwD;AAEzF;;;AAGG;AACc,IAAA,QAAQ,GAAG,IAAI,GAAG,EAAmD;AAEtF;;AAEG;AACc,IAAA,KAAK,GAAG,IAAI,GAAG,EAAQ;AAExC;;AAEG;AACc,IAAA,QAAQ;AAEzB,IAAA,WAAA,CACqC,GAAa,EACf,KAAa,EACE,KAAqB,EAChD,aAAqB,EAAE,EAAA;QAHT,IAAG,CAAA,GAAA,GAAH,GAAG;QACL,IAAK,CAAA,KAAA,GAAL,KAAK;QACU,IAAK,CAAA,KAAA,GAAL,KAAK;AAGrD,QAAA,IAAI,CAAC,QAAQ,GAAG,gBAAgB,CAAC,UAAU,CAAC;AAC5C,QAAA,eAAe,CAAC,GAAG,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC;QACvD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;;AAG1B;;;AAGG;IACH,SAAS,CAAC,MAAgB,EAAE,IAAe,EAAA;AACzC,QAAA,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,kBAAkB,CAAC;;QAGvD,IAAI,EAAE,OAAO,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,iBAAiB,CAAC,CAAC;;AAGlF;;;AAGG;IACH,YAAY,CAAC,MAAgB,EAAE,IAAe,EAAA;AAC5C,QAAA,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,CAAC;;AAGtC,QAAA,IAAI,EAAE,OAAO,CAAC,CAAC,KAAK,KAAK,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;;AAGxD,IAAA,QAAQ,CAChB,KAAa,EACb,MAAmC,EACnC,OAA4C,EAAA;;QAG5C,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;;QAGhC,IAAI,MAAM,EAAE;AACV,YAAA,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,MAAM,CAAC,KAAK,KAAK,CAAC,EAAE;;;AAGzE,gBAAA,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,OAAO,CAAC,YAAY,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;;YAEnF,MAAM,CAAC,KAAK,EAAE;;aACT;;AAEL,YAAA,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE;AAChB,gBAAA,KAAK,EAAE,CAAC;AACR,gBAAA,QAAQ,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AACzF,aAAA,CAAC;;;IAII,WAAW,CACnB,KAAa,EACb,MAAmC,EAAA;;QAGnC,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;;;QAIhC,IAAI,MAAM,EAAE;YACV,MAAM,CAAC,KAAK,EAAE;AACd,YAAA,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC,EAAE;AACrB,gBAAA,cAAc,CAAC,MAAM,CAAC,QAAQ,CAAC;AAC/B,gBAAA,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC;;;;IAK1B,WAAW,GAAA;QACT,KAAK,MAAM,GAAG,EAAC,QAAQ,EAAC,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE;YAC/D,cAAc,CAAC,QAAQ,CAAC;;AAE1B,QAAA,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;;AAGpB;;;;;AAKG;AACH,IAAA,OAAO,CAAC,QAAc,EAAA;AACpB,QAAA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC;;AAGxB,QAAA,KAAK,MAAM,CAAC,KAAK,EAAE,EAAC,QAAQ,EAAC,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;AAC7C,YAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,kBAAkB,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;AAE/E,QAAA,KAAK,MAAM,CAAC,GAAG,EAAE,EAAC,QAAQ,EAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;AAC7C,YAAA,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,iBAAiB,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;;;AAI9E,IAAA,UAAU,CAAC,QAAc,EAAA;AACvB,QAAA,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC;;IAGrB,UAAU,CAAwB,IAAU,EAAE,OAAU,EAAA;;AAE9D,QAAA,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC;;;AAI3C,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAO,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC;;;AAIzD,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC;;AA9IvB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,kBAwBjB,QAAQ,EAAA,EAAA,EAAA,KAAA,EACR,MAAM,EACN,EAAA,EAAA,KAAA,EAAA,SAAS,6BACT,WAAW,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHA3BV,gBAAgB,EAAA,CAAA;;sGAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B;;0BAyBI,MAAM;2BAAC,QAAQ;;0BACf,MAAM;2BAAC,MAAM;;0BACb,MAAM;2BAAC,SAAS;;0BAAG;;0BACnB,MAAM;2BAAC,WAAW;;;AChGhB,MAAM,cAAc,GAA2B;AACpD,IAAA,KAAK,EAAE,4BAA4B;AACnC,IAAA,OAAO,EAAE,8BAA8B;AACvC,IAAA,OAAO,EAAE,8BAA8B;AACvC,IAAA,KAAK,EAAE,sCAAsC;AAC7C,IAAA,OAAO,EAAE,+BAA+B;AACxC,IAAA,MAAM,EAAE,oCAAoC;CAC7C;AAED,MAAM,eAAe,GAAG,SAAS;AACjC,MAAM,oBAAoB,GAAG,uCAAuC;AACpE,MAAM,eAAe,GAAG,UAAU;AAE3B,MAAM,kBAAkB,GAAG,QAAQ;AACnC,MAAM,SAAS,GAAG,CAAW,QAAA,EAAA,kBAAkB,EAAE;AACjD,MAAM,YAAY,GAAG,CAAc,WAAA,EAAA,kBAAkB,EAAE;AAE9D;;AAEG;AACH,MAAM,0CAA0C,GAAG,IAAI;AAEvD;;;;;;AAMG;AACU,MAAA,kCAAkC,GAAG,IAAI,cAAc,CAClE,SAAS,GAAG,2BAA2B,GAAG,EAAE,EAC5C;AACE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,MAAM,0CAA0C;AAC1D,CAAA;AAGG,SAAU,oBAAoB,CAAC,gBAAwB,EAAA;IAC3D,OAAO,YAAY,CAAC,OAAO,CAAC,eAAe,EAAE,gBAAgB,CAAC;AAChE;AAEM,SAAU,iBAAiB,CAAC,gBAAwB,EAAA;IACxD,OAAO,SAAS,CAAC,OAAO,CAAC,eAAe,EAAE,gBAAgB,CAAC;AAC7D;AAEgB,SAAA,iBAAiB,CAAC,MAAc,EAAE,MAAgB,EAAA;AAChE,IAAA,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;AAC9D;AAEA;;;;;;;;;;;;;;;;;AAiBG;AACa,SAAA,yBAAyB,CAAC,QAAgB,EAAE,MAAgB,EAAA;IAC1E,IAAI,CAAC,QAAQ,EAAE;AACb,QAAA,OAAO,MAAM;;IAGf,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAC,QAAQ,EAAE,kBAAkB,CAAC;AAEjE,IAAA,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,UAAU,KAAI;QAC/B,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,mBAAmB,CAAC,EAAE;AAC7C,YAAA,OAAO,UAAU;;QAGnB,OAAO,UAAU,CAAC,OAAO,CAAC,oBAAoB,EAAE,CAAC,CAAC,EAAE,YAAY,KAAI;AAClE,YAAA,IACE,YAAY,CAAC,CAAC,CAAC,KAAK,GAAG;AACvB,gBAAA,YAAY,CAAC,UAAU,CAAC,OAAO,CAAC;AAChC,gBAAA,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,EAClC;gBACA,OAAO,CAAA,qBAAA,EAAwB,YAAY,CAAA,GAAA,CAAK;;AAGlD,YAAA,MAAM,EAAC,QAAQ,EAAE,oBAAoB,EAAC,GAAG,IAAI,GAAG,CAAC,YAAY,EAAE,mBAAmB,CAAC;YAEnF,OAAO,CAAA,qBAAA,EAAwB,oBAAoB,CAAA,GAAA,CAAK;AAC1D,SAAC,CAAC;AACJ,KAAC,CAAC;AACJ;MAGa,mBAAmB,CAAA;AASX,IAAA,YAAA;AACA,IAAA,gBAAA;AACgB,IAAA,KAAA;AACmB,IAAA,yBAAA;AACjB,IAAA,GAAA;AACL,IAAA,UAAA;AACrB,IAAA,MAAA;AAC2B,IAAA,KAAA;AAGnB,IAAA,cAAA;AAlBF,IAAA,gBAAgB,GAAG,IAAI,GAAG,EAGxC;AACc,IAAA,eAAe;AACf,IAAA,gBAAgB;AAEjC,IAAA,WAAA,CACmB,YAA0B,EAC1B,gBAAkC,EAClB,KAAa,EACM,yBAAkC,EACnD,GAAa,EAClB,UAAkB,EACvC,MAAc,EACa,QAAuB,IAAI,EAG9C,iBAAyD,IAAI,EAAA;QAV7D,IAAY,CAAA,YAAA,GAAZ,YAAY;QACZ,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB;QACA,IAAK,CAAA,KAAA,GAAL,KAAK;QACc,IAAyB,CAAA,yBAAA,GAAzB,yBAAyB;QAC1C,IAAG,CAAA,GAAA,GAAH,GAAG;QACR,IAAU,CAAA,UAAA,GAAV,UAAU;QAC/B,IAAM,CAAA,MAAA,GAAN,MAAM;QACqB,IAAK,CAAA,KAAA,GAAL,KAAK;QAGxB,IAAc,CAAA,cAAA,GAAd,cAAc;QAE/B,IAAI,CAAC,gBAAgB,GAAG,OAAO,YAAY,KAAK,WAAW,IAAI,YAAY;QAC3E,IAAI,CAAC,eAAe,GAAG,IAAI,mBAAmB,CAC5C,YAAY,EACZ,GAAG,EACH,MAAM,EACN,IAAI,CAAC,gBAAgB,EACrB,IAAI,CAAC,cAAc,CACpB;;IAGH,cAAc,CAAC,OAAY,EAAE,IAA0B,EAAA;AACrD,QAAA,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,EAAE;YACrB,OAAO,IAAI,CAAC,eAAe;;QAG7B,IACE,OAAO,YAAY,KAAK,WAAW;YACnC,YAAY;AACZ,YAAA,IAAI,CAAC,aAAa,KAAK,iBAAiB,CAAC,SAAS,EAClD;;YAEA,IAAI,GAAG,EAAC,GAAG,IAAI,EAAE,aAAa,EAAE,iBAAiB,CAAC,QAAQ,EAAC;;QAG7D,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC;;;AAGxD,QAAA,IAAI,QAAQ,YAAY,iCAAiC,EAAE;AACzD,YAAA,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC;;AACxB,aAAA,IAAI,QAAQ,YAAY,4BAA4B,EAAE;YAC3D,QAAQ,CAAC,WAAW,EAAE;;AAGxB,QAAA,OAAO,QAAQ;;IAGT,mBAAmB,CAAC,OAAY,EAAE,IAAmB,EAAA;AAC3D,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB;QAC9C,IAAI,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QAE5C,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG;AACpB,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM;AAC1B,YAAA,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY;AACtC,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB;AAC9C,YAAA,MAAM,yBAAyB,GAAG,IAAI,CAAC,yBAAyB;AAChE,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB;AAC9C,YAAA,MAAM,cAAc,GAAG,IAAI,CAAC,cAAc;AAE1C,YAAA,QAAQ,IAAI,CAAC,aAAa;gBACxB,KAAK,iBAAiB,CAAC,QAAQ;oBAC7B,QAAQ,GAAG,IAAI,iCAAiC,CAC9C,YAAY,EACZ,gBAAgB,EAChB,IAAI,EACJ,IAAI,CAAC,KAAK,EACV,yBAAyB,EACzB,GAAG,EACH,MAAM,EACN,gBAAgB,EAChB,cAAc,CACf;oBACD;gBACF,KAAK,iBAAiB,CAAC,SAAS;oBAC9B,OAAO,IAAI,iBAAiB,CAC1B,YAAY,EACZ,gBAAgB,EAChB,OAAO,EACP,IAAI,EACJ,GAAG,EACH,MAAM,EACN,IAAI,CAAC,KAAK,EACV,gBAAgB,EAChB,cAAc,CACf;AACH,gBAAA;oBACE,QAAQ,GAAG,IAAI,4BAA4B,CACzC,YAAY,EACZ,gBAAgB,EAChB,IAAI,EACJ,yBAAyB,EACzB,GAAG,EACH,MAAM,EACN,gBAAgB,EAChB,cAAc,CACf;oBACD;;YAGJ,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC;;AAGzC,QAAA,OAAO,QAAQ;;IAGjB,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE;;AAG/B;;;AAGG;AACO,IAAA,iBAAiB,CAAC,WAAmB,EAAA;AAC7C,QAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC;;kHA7HhC,mBAAmB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAC,YAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,gBAAA,EAAA,EAAA,EAAA,KAAA,EAWpB,MAAM,EAAA,EAAA,EAAA,KAAA,EACN,kCAAkC,EAAA,EAAA,EAAA,KAAA,EAClC,QAAQ,EAAA,EAAA,EAAA,KAAA,EACR,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAEX,SAAS,EAAA,EAAA,EAAA,KAAA,EACTC,eAAc,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;sHAjBb,mBAAmB,EAAA,CAAA;;sGAAnB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAD/B;;0BAYI,MAAM;2BAAC,MAAM;;0BACb,MAAM;2BAAC,kCAAkC;;0BACzC,MAAM;2BAAC,QAAQ;;0BACf,MAAM;2BAAC,WAAW;;0BAElB,MAAM;2BAAC,SAAS;;0BAChB,MAAM;2BAACA,eAAc;;0BACrB;;AA+GL,MAAM,mBAAmB,CAAA;AAUJ,IAAA,YAAA;AACA,IAAA,GAAA;AACA,IAAA,MAAA;AACA,IAAA,gBAAA;AACA,IAAA,cAAA;AAbnB,IAAA,IAAI,GAAyB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;AAEhD;;;AAGG;IACH,qBAAqB,GAAG,IAAI;IAE5B,WACmB,CAAA,YAA0B,EAC1B,GAAa,EACb,MAAc,EACd,gBAAyB,EACzB,cAAsD,EAAA;QAJtD,IAAY,CAAA,YAAA,GAAZ,YAAY;QACZ,IAAG,CAAA,GAAA,GAAH,GAAG;QACH,IAAM,CAAA,MAAA,GAAN,MAAM;QACN,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB;QAChB,IAAc,CAAA,cAAA,GAAd,cAAc;;AAGjC,IAAA,OAAO;IAEP,WAAW,GAAG,IAAI;IAElB,aAAa,CAAC,IAAY,EAAE,SAAkB,EAAA;QAC5C,IAAI,SAAS,EAAE;;;;;;;;;;AAUb,YAAA,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,SAAS,EAAE,IAAI,CAAC;;QAG/E,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC;;AAGrC,IAAA,aAAa,CAAC,KAAa,EAAA;QACzB,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC;;AAGtC,IAAA,UAAU,CAAC,KAAa,EAAA;QACtB,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC;;IAGvC,WAAW,CAAC,MAAW,EAAE,QAAa,EAAA;AACpC,QAAA,MAAM,YAAY,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,GAAG,MAAM;AACrE,QAAA,YAAY,CAAC,WAAW,CAAC,QAAQ,CAAC;;AAGpC,IAAA,YAAY,CAAC,MAAW,EAAE,QAAa,EAAE,QAAa,EAAA;QACpD,IAAI,MAAM,EAAE;AACV,YAAA,MAAM,YAAY,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,GAAG,MAAM;AACrE,YAAA,YAAY,CAAC,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC;;;IAIjD,WAAW,CAAC,OAAY,EAAE,QAAa,EAAA;QACrC,QAAQ,CAAC,MAAM,EAAE;;IAGnB,iBAAiB,CAAC,cAA4B,EAAE,eAAyB,EAAA;QACvE,IAAI,EAAE,GACJ,OAAO,cAAc,KAAK,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,cAAc,CAAC,GAAG,cAAc;QAC9F,IAAI,CAAC,EAAE,EAAE;YACP,MAAM,IAAIH,aAAY,CAAA,CAAA,IAAA,6CAEpB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;gBAC5C,CAAiB,cAAA,EAAA,cAAc,CAA8B,4BAAA,CAAA,CAChE;;QAEH,IAAI,CAAC,eAAe,EAAE;AACpB,YAAA,EAAE,CAAC,WAAW,GAAG,EAAE;;AAErB,QAAA,OAAO,EAAE;;AAGX,IAAA,UAAU,CAAC,IAAS,EAAA;QAClB,OAAO,IAAI,CAAC,UAAU;;AAGxB,IAAA,WAAW,CAAC,IAAS,EAAA;QACnB,OAAO,IAAI,CAAC,WAAW;;AAGzB,IAAA,YAAY,CAAC,EAAO,EAAE,IAAY,EAAE,KAAa,EAAE,SAAkB,EAAA;QACnE,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,GAAG,SAAS,GAAG,GAAG,GAAG,IAAI;AAC7B,YAAA,MAAM,YAAY,GAAG,cAAc,CAAC,SAAS,CAAC;YAC9C,IAAI,YAAY,EAAE;gBAChB,EAAE,CAAC,cAAc,CAAC,YAAY,EAAE,IAAI,EAAE,KAAK,CAAC;;iBACvC;AACL,gBAAA,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC;;;aAEzB;AACL,YAAA,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC;;;AAIhC,IAAA,eAAe,CAAC,EAAO,EAAE,IAAY,EAAE,SAAkB,EAAA;QACvD,IAAI,SAAS,EAAE;AACb,YAAA,MAAM,YAAY,GAAG,cAAc,CAAC,SAAS,CAAC;YAC9C,IAAI,YAAY,EAAE;AAChB,gBAAA,EAAE,CAAC,iBAAiB,CAAC,YAAY,EAAE,IAAI,CAAC;;iBACnC;gBACL,EAAE,CAAC,eAAe,CAAC,CAAA,EAAG,SAAS,CAAI,CAAA,EAAA,IAAI,CAAE,CAAA,CAAC;;;aAEvC;AACL,YAAA,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;;;IAI5B,QAAQ,CAAC,EAAO,EAAE,IAAY,EAAA;AAC5B,QAAA,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC;;IAGxB,WAAW,CAAC,EAAO,EAAE,IAAY,EAAA;AAC/B,QAAA,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC;;AAG3B,IAAA,QAAQ,CAAC,EAAO,EAAE,KAAa,EAAE,KAAU,EAAE,KAA0B,EAAA;AACrE,QAAA,IAAI,KAAK,IAAI,mBAAmB,CAAC,QAAQ,GAAG,mBAAmB,CAAC,SAAS,CAAC,EAAE;YAC1E,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,GAAG,mBAAmB,CAAC,SAAS,GAAG,WAAW,GAAG,EAAE,CAAC;;aACvF;AACL,YAAA,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK;;;AAI3B,IAAA,WAAW,CAAC,EAAO,EAAE,KAAa,EAAE,KAA0B,EAAA;AAC5D,QAAA,IAAI,KAAK,GAAG,mBAAmB,CAAC,QAAQ,EAAE;;AAExC,YAAA,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC;;aACzB;AACL,YAAA,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE;;;AAIxB,IAAA,WAAW,CAAC,EAAO,EAAE,IAAY,EAAE,KAAU,EAAA;AAC3C,QAAA,IAAI,EAAE,IAAI,IAAI,EAAE;YACd;;AAGF,QAAA,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC5C,YAAA,IAAI,CAAC,qBAAqB;AAC1B,YAAA,oBAAoB,CAAC,IAAI,EAAE,UAAU,CAAC;AACxC,QAAA,EAAE,CAAC,IAAI,CAAC,GAAG,KAAK;;IAGlB,QAAQ,CAAC,IAAS,EAAE,KAAa,EAAA;AAC/B,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK;;AAGxB,IAAA,MAAM,CACJ,MAA4C,EAC5C,KAAa,EACb,QAAiC,EACjC,OAAyB,EAAA;AAEzB,QAAA,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC5C,YAAA,IAAI,CAAC,qBAAqB;AAC1B,YAAA,oBAAoB,CAAC,KAAK,EAAE,UAAU,CAAC;AACzC,QAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAC9B,YAAA,MAAM,GAAGI,OAAM,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC;YACxD,IAAI,CAAC,MAAM,EAAE;gBACX,MAAM,IAAIJ,aAAY,CAAA,IAAA,kDAEpB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC5C,oBAAA,CAAA,yBAAA,EAA4B,MAAM,CAAA,WAAA,EAAc,KAAK,CAAA,CAAE,CAC1D;;;QAIL,IAAI,eAAe,GAAG,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC;AAE3D,QAAA,IAAI,IAAI,CAAC,cAAc,EAAE,iBAAiB,EAAE;AAC1C,YAAA,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,eAAe,CAAC;;AAGzF,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB,CACvC,MAAM,EACN,KAAK,EACL,eAAe,EACf,OAAO,CACQ;;AAGX,IAAA,sBAAsB,CAAC,YAAsB,EAAA;;;;;QAKnD,OAAO,CAAC,KAAU,KAAI;;;;;;AAMpB,YAAA,IAAI,KAAK,KAAK,cAAc,EAAE;AAC5B,gBAAA,OAAO,YAAY;;;;AAKrB,YAAA,MAAM,oBAAoB,GACxB,OAAO,YAAY,KAAK,WAAW,IAAI;AACrC,kBAAE,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,YAAY,CAAC,KAAK,CAAC;AAClD,kBAAE,YAAY,CAAC,KAAK,CAAC;AACzB,YAAA,IAAI,oBAAoB,KAAK,KAAK,EAAE;gBAClC,KAAK,CAAC,cAAc,EAAE;;AAGxB,YAAA,OAAO,SAAS;AAClB,SAAC;;AAEJ;AAED,MAAM,WAAW,GAAG,CAAC,MAAM,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG;AAC/C,SAAS,oBAAoB,CAAC,IAAY,EAAE,QAAgB,EAAA;IAC1D,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;AACtC,QAAA,MAAM,IAAIA,aAAY,CAAA,IAAA,uDAEpB,CAAwB,qBAAA,EAAA,QAAQ,IAAI,IAAI,CAAA;;+DAEiB,IAAI,CAAA,+HAAA,CAAiI,CAC/L;;AAEL;AAEA,SAAS,cAAc,CAAC,IAAS,EAAA;IAC/B,OAAO,IAAI,CAAC,OAAO,KAAK,UAAU,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS;AAClE;AAEA,MAAM,iBAAkB,SAAQ,mBAAmB,CAAA;AAKvC,IAAA,gBAAA;AACA,IAAA,MAAA;AALF,IAAA,UAAU;AAElB,IAAA,WAAA,CACE,YAA0B,EAClB,gBAAkC,EAClC,MAAW,EACnB,SAAwB,EACxB,GAAa,EACb,MAAc,EACd,KAAoB,EACpB,gBAAyB,EACzB,cAAsD,EAAA;QAEtD,KAAK,CAAC,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,gBAAgB,EAAE,cAAc,CAAC;QAT1D,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB;QAChB,IAAM,CAAA,MAAA,GAAN,MAAM;AASd,QAAA,IAAI,CAAC,UAAU,GAAI,MAAc,CAAC,YAAY,CAAC,EAAC,IAAI,EAAE,MAAM,EAAC,CAAC;QAC9D,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC;AAC9C,QAAA,IAAI,MAAM,GAAG,SAAS,CAAC,MAAM;QAC7B,IAAI,SAAS,EAAE;;YAEb,MAAM,QAAQ,GAAGI,OAAM,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE;AAChD,YAAA,MAAM,GAAG,yBAAyB,CAAC,QAAQ,EAAE,MAAM,CAAC;;QAGtD,MAAM,GAAG,iBAAiB,CAAC,SAAS,CAAC,EAAE,EAAE,MAAM,CAAC;AAEhD,QAAA,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC;YAE/C,IAAI,KAAK,EAAE;AACT,gBAAA,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC;;AAGtC,YAAA,OAAO,CAAC,WAAW,GAAG,KAAK;AAC3B,YAAA,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC;;;;;;;;AAStC,QAAA,MAAM,SAAS,GAAG,SAAS,CAAC,iBAAiB,IAAI;QACjD,IAAI,SAAS,EAAE;AACb,YAAA,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;gBAChC,MAAM,MAAM,GAAG,iBAAiB,CAAC,QAAQ,EAAE,GAAG,CAAC;gBAC/C,IAAI,KAAK,EAAE;AACT,oBAAA,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC;;AAErC,gBAAA,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC;;;;AAKjC,IAAA,gBAAgB,CAAC,IAAS,EAAA;AAChC,QAAA,OAAO,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI;;IAG7C,WAAW,CAAC,MAAW,EAAE,QAAa,EAAA;AAC7C,QAAA,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC;;AAE1D,IAAA,YAAY,CAAC,MAAW,EAAE,QAAa,EAAE,QAAa,EAAA;AAC7D,QAAA,OAAO,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC;;IAErE,WAAW,CAAC,OAAY,EAAE,QAAa,EAAA;QAC9C,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC;;AAEjC,IAAA,UAAU,CAAC,IAAS,EAAA;AAC3B,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC;;IAGpE,OAAO,GAAA;QACd,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;;AAEpD;AAED,MAAM,4BAA6B,SAAQ,mBAAmB,CAAA;AAMzC,IAAA,gBAAA;AAET,IAAA,yBAAA;AAPO,IAAA,MAAM;AACN,IAAA,SAAS;AAE1B,IAAA,WAAA,CACE,YAA0B,EACT,gBAAkC,EACnD,SAAwB,EAChB,yBAAkC,EAC1C,GAAa,EACb,MAAc,EACd,gBAAyB,EACzB,cAAsD,EACtD,MAAe,EAAA;QAEf,KAAK,CAAC,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,gBAAgB,EAAE,cAAc,CAAC;QATjD,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB;QAEzB,IAAyB,CAAA,yBAAA,GAAzB,yBAAyB;AAQjC,QAAA,IAAI,MAAM,GAAG,SAAS,CAAC,MAAM;QAC7B,IAAI,SAAS,EAAE;;YAEb,MAAM,QAAQ,GAAGA,OAAM,EAAE,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,EAAE;AAChD,YAAA,MAAM,GAAG,yBAAyB,CAAC,QAAQ,EAAE,MAAM,CAAC;;AAGtD,QAAA,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,iBAAiB,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,MAAM;QACjE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,iBAAiB,GAAG,MAAM,CAAC;;IAGxD,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC;;IAGrD,OAAO,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACnC;;AAGF,QAAA,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC;;AAElE;AAED,MAAM,iCAAkC,SAAQ,4BAA4B,CAAA;AAClE,IAAA,WAAW;AACX,IAAA,QAAQ;AAEhB,IAAA,WAAA,CACE,YAA0B,EAC1B,gBAAkC,EAClC,SAAwB,EACxB,KAAa,EACb,yBAAkC,EAClC,GAAa,EACb,MAAc,EACd,gBAAyB,EACzB,cAAsD,EAAA;QAEtD,MAAM,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,SAAS,CAAC,EAAE;AACzC,QAAA,KAAK,CACH,YAAY,EACZ,gBAAgB,EAChB,SAAS,EACT,yBAAyB,EACzB,GAAG,EACH,MAAM,EACN,gBAAgB,EAChB,cAAc,EACd,MAAM,CACP;AACD,QAAA,IAAI,CAAC,WAAW,GAAG,oBAAoB,CAAC,MAAM,CAAC;AAC/C,QAAA,IAAI,CAAC,QAAQ,GAAG,iBAAiB,CAAC,MAAM,CAAC;;AAG3C,IAAA,WAAW,CAAC,OAAY,EAAA;QACtB,IAAI,CAAC,WAAW,EAAE;QAClB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;;IAGtC,aAAa,CAAC,MAAW,EAAE,IAAY,EAAA;QAC9C,MAAM,EAAE,GAAG,KAAK,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC;QAC5C,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;AAC5C,QAAA,OAAO,EAAE;;AAEZ;;;;"}