
      import {createRequire as __cjsCompatRequire} from 'module';
      const require = __cjsCompatRequire(import.meta.url);
    
import {
  DynamicValue,
  ImportManager,
  PartialEvaluator,
  PotentialImportKind,
  PotentialImportMode,
  Reference,
  StaticInterpreter,
  TypeScriptReflectionHost,
  createForwardRefResolver,
  reflectObjectLiteral
} from "../chunk-SILQIVD4.js";
import "../chunk-LMRFLQ2K.js";
import "../chunk-TPEB2IXF.js";
import "../chunk-3NKMA2JO.js";
import "../chunk-KPQ72R34.js";
export {
  DynamicValue,
  ImportManager,
  PartialEvaluator,
  PotentialImportKind,
  PotentialImportMode,
  Reference,
  StaticInterpreter,
  TypeScriptReflectionHost,
  createForwardRefResolver,
  reflectObjectLiteral
};
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
//# sourceMappingURL=migrations.js.map
