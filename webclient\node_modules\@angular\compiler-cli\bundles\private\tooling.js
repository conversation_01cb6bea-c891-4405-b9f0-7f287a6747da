
      import {createRequire as __cjsCompatRequire} from 'module';
      const require = __cjsCompatRequire(import.meta.url);
    
import {
  GLOBAL_DEFS_FOR_TERSER,
  GLOBAL_DEFS_FOR_TERSER_WITH_AOT,
  constructorParametersDownlevelTransform
} from "../chunk-M7MXAQMK.js";
import "../chunk-7FGS2TFN.js";
import "../chunk-SILQIVD4.js";
import "../chunk-LMRFLQ2K.js";
import "../chunk-TPEB2IXF.js";
import "../chunk-3NKMA2JO.js";
import "../chunk-KPQ72R34.js";
export {
  GLOBAL_DEFS_FOR_TERSER,
  GLOBAL_DEFS_FOR_TERSER_WITH_AOT,
  constructorParametersDownlevelTransform
};
//# sourceMappingURL=tooling.js.map
