extends Control

# Button references
@onready var poisoning_pigeons_button: Button = $VBoxContainer/GameButtonsContainer/TopRow/PoisoningPigeonsButton
@onready var florida_man_button: Button = $VBoxContainer/GameButtonsContainer/TopRow/FloridaManButton
@onready var washington_path_button: Button = $VBoxContainer/GameButtonsContainer/BottomRow/WashingtonPathButton
@onready var pick_your_nose_button: Button = $VBoxContainer/GameButtonsContainer/BottomRow/PickYourNoseButton
@onready var settings_button: Button = $VBoxContainer/SettingsButton

# Connection status label
@onready var connection_status: Label = $VBoxContainer/ConnectionStatus

func _ready():
	# Connect button signals
	poisoning_pigeons_button.pressed.connect(_on_poisoning_pigeons_pressed)
	florida_man_button.pressed.connect(_on_florida_man_pressed)
	washington_path_button.pressed.connect(_on_washington_path_pressed)
	pick_your_nose_button.pressed.connect(_on_pick_your_nose_pressed)
	settings_button.pressed.connect(_on_settings_pressed)
	
	# Update connection status
	update_connection_status()
	
	# Focus the first button for controller navigation
	poisoning_pigeons_button.grab_focus()

func update_connection_status():
	if GameSettings.is_connected_to_server:
		connection_status.text = "Connected to Server"
		connection_status.modulate = Color.GREEN
	else:
		connection_status.text = "Offline Mode"
		connection_status.modulate = Color.ORANGE

func _on_poisoning_pigeons_pressed():
	print("Starting Poisoning Pigeons minigame")
	GameSettings.change_scene(GameSettings.POISONING_PIGEONS_SCENE)

func _on_florida_man_pressed():
	print("Starting Florida Man minigame")
	GameSettings.change_scene(GameSettings.FLORIDA_MAN_SCENE)

func _on_washington_path_pressed():
	print("Starting Washington Path minigame")
	GameSettings.change_scene(GameSettings.WASHINGTON_PATH_SCENE)

func _on_pick_your_nose_pressed():
	print("Starting Pick Your Own Nose minigame")
	GameSettings.change_scene(GameSettings.PICK_YOUR_NOSE_SCENE)

func _on_settings_pressed():
	print("Opening settings")
	GameSettings.change_scene(GameSettings.SETTINGS_SCENE)
