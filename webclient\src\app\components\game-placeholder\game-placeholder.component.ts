import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Game, Player } from '../../services/game.service';

@Component({
  selector: 'app-game-placeholder',
  template: `
    <ion-card>
      <ion-card-header>
        <ion-card-title>{{ gameTitle }}</ion-card-title>
        <ion-card-subtitle>Coming Soon!</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        <div class="placeholder-content">
          <ion-icon name="construct-outline" size="large" color="warning"></ion-icon>
          <h3>This game is under development</h3>
          <p>{{ gameTitle }} will be available in a future update.</p>
          <p>Current status: {{ game.game_status }}</p>
          
          <div *ngIf="player?.is_leader" class="leader-controls">
            <ion-button expand="block" (click)="goBackToMenu()" fill="outline">
              Return to Main Menu
            </ion-button>
          </div>
        </div>
      </ion-card-content>
    </ion-card>
  `,
  styles: [`
    .placeholder-content {
      text-align: center;
      padding: 40px 20px;
      
      ion-icon {
        margin-bottom: 20px;
      }
      
      h3 {
        margin: 0 0 16px 0;
        font-size: 1.3rem;
        font-weight: 600;
      }
      
      p {
        margin: 8px 0;
        color: var(--ion-color-medium);
      }
      
      .leader-controls {
        margin-top: 24px;
      }
    }
  `],
  imports: [CommonModule, IonicModule],
})
export class GamePlaceholderComponent {
  @Input() game!: Game;
  @Input() player!: Player;
  @Input() gameTitle!: string;
  @Output() actionSubmitted = new EventEmitter<{ type: string; data: any }>();

  goBackToMenu() {
    this.actionSubmitted.emit({
      type: 'end_game',
      data: {}
    });
  }
}
