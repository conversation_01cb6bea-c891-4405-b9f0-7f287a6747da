extends Control

@onready var loading_label: Label = $VBoxContainer/LoadingLabel
@onready var progress_bar: ProgressBar = $VBoxContainer/ProgressBar
@onready var status_label: Label = $VBoxContainer/StatusLabel

var http_request: HTTPRequest
var connection_attempts: int = 0
var max_connection_attempts: int = 5
var connection_timeout: float = 5.0

func _ready():
	# Initialize UI
	loading_label.text = "JUGSHINE"
	status_label.text = "Initializing..."
	progress_bar.value = 0
	
	# Create HTTP request node
	http_request = HTTPRequest.new()
	add_child(http_request)
	http_request.request_completed.connect(_on_request_completed)
	http_request.timeout = connection_timeout
	
	# Start connection process
	start_connection_process()

func start_connection_process():
	status_label.text = "Connecting to server..."
	progress_bar.value = 20
	
	# Attempt to connect to the web server
	attempt_server_connection()

func attempt_server_connection():
	connection_attempts += 1
	status_label.text = "Connecting to server... (Attempt %d/%d)" % [connection_attempts, max_connection_attempts]
	
	# Make a simple GET request to check if server is available
	var headers = ["Content-Type: application/json"]
	var error = http_request.request(GameSettings.server_url + "/health", headers, HTTPClient.METHOD_GET)
	
	if error != OK:
		print("Failed to make HTTP request: ", error)
		handle_connection_failure()

func _on_request_completed(result: int, response_code: int, headers: PackedStringArray, body: PackedByteArray):
	if response_code == 200:
		# Successfully connected
		handle_connection_success()
	else:
		# Connection failed
		print("Server connection failed. Response code: ", response_code)
		handle_connection_failure()

func handle_connection_success():
	status_label.text = "Connected to server!"
	progress_bar.value = 60
	
	# Set connection status in GameSettings
	GameSettings.is_connected_to_server = true
	
	# Simulate additional loading steps
	await simulate_loading_steps()
	
	# Transition to main menu
	transition_to_main_menu()

func handle_connection_failure():
	if connection_attempts < max_connection_attempts:
		# Wait a bit and try again
		status_label.text = "Connection failed. Retrying in 2 seconds..."
		await get_tree().create_timer(2.0).timeout
		attempt_server_connection()
	else:
		# Max attempts reached, continue offline
		status_label.text = "Could not connect to server. Continuing offline..."
		progress_bar.value = 60
		GameSettings.is_connected_to_server = false
		
		# Still simulate loading and continue to main menu
		await simulate_loading_steps()
		transition_to_main_menu()

func simulate_loading_steps():
	# Simulate loading game assets
	status_label.text = "Loading game assets..."
	progress_bar.value = 80
	await get_tree().create_timer(1.0).timeout
	
	# Simulate final initialization
	status_label.text = "Finalizing..."
	progress_bar.value = 100
	await get_tree().create_timer(0.5).timeout

func transition_to_main_menu():
	status_label.text = "Ready!"
	await get_tree().create_timer(0.5).timeout
	
	# Change to main menu scene
	GameSettings.change_scene(GameSettings.MAIN_MENU_SCENE)
