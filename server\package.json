{"name": "jugshine-server", "version": "1.0.0", "description": "Multi-game server for Jugshine party games", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "socket.io": "^4.7.4", "dotenv": "^16.3.1", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "keywords": ["game", "multiplayer", "jackbox", "party-game"], "author": "Jugshine Team", "license": "MIT"}