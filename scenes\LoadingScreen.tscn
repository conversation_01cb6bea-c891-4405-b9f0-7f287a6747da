[gd_scene load_steps=2 format=3 uid="uid://bqxvn8ywqkqxr"]

[ext_resource type="Script" uid="uid://c7ljmcapj8p63" path="res://scripts/LoadingScreen.gd" id="1_0s8vr"]

[node name="LoadingScreen" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_0s8vr")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
color = Color(0.1, 0.1, 0.15, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -100.0
offset_right = 200.0
offset_bottom = 100.0
grow_horizontal = 2
grow_vertical = 2

[node name="LoadingLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 48
text = "JUGSHINE"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
modulate = Color(1, 1, 1, 0)
layout_mode = 2

[node name="StatusLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 18
text = "Initializing..."
horizontal_alignment = 1

[node name="ProgressBar" type="ProgressBar" parent="VBoxContainer"]
layout_mode = 2
step = 1.0
show_percentage = false
