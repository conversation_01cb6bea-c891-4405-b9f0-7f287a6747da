# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/jugshine
MONGODB_TEST_URI=mongodb://localhost:27017/jugshine_test

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:8100,http://localhost:4200

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Game Configuration
MAX_PLAYERS_PER_GAME=8
GAME_CLEANUP_INTERVAL_MS=300000
INACTIVE_GAME_TIMEOUT_MS=1800000
