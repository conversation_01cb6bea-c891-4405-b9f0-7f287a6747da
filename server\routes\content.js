const express = require('express');
const { param, query, validationResult } = require('express-validator');
const GameContent = require('../models/GameContent');

const router = express.Router();

// Validation middleware
const validateGameType = param('gameType').isIn(['poisoning_pigeons', 'florida_man', 'washington_path', 'pick_your_nose']);
const validateContentType = param('contentType').isAlpha();

const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

// Get content for a specific game type
router.get('/:gameType/:contentType', [
  validateGameType,
  validateContentType,
  query('mature_content').optional().isBoolean(),
  query('count').optional().isInt({ min: 1, max: 100 }),
  handleValidationErrors
], async (req, res) => {
  try {
    const { gameType, contentType } = req.params;
    const matureContent = req.query.mature_content === 'true';
    const count = parseInt(req.query.count) || null;
    
    let content;
    
    if (count) {
      // Get random items
      const result = await GameContent.getRandomItems(gameType, contentType, count, matureContent);
      content = result.length > 0 ? result[0].items : [];
    } else {
      // Get all content
      const contentDoc = await GameContent.getContent(gameType, contentType, matureContent);
      content = contentDoc ? contentDoc.data : [];
    }
    
    res.json({
      game_type: gameType,
      content_type: contentType,
      mature_content: matureContent,
      count: content.length,
      data: content
    });
    
  } catch (error) {
    console.error('Error fetching content:', error);
    res.status(500).json({ error: 'Failed to fetch content' });
  }
});

// Get random prompt for a game
router.get('/:gameType/random-prompt', [
  validateGameType,
  query('mature_content').optional().isBoolean(),
  handleValidationErrors
], async (req, res) => {
  try {
    const { gameType } = req.params;
    const matureContent = req.query.mature_content === 'true';
    
    let contentType;
    switch (gameType) {
      case 'poisoning_pigeons':
        contentType = 'prompts';
        break;
      case 'florida_man':
        contentType = 'scenarios';
        break;
      case 'washington_path':
        contentType = 'story_nodes';
        break;
      case 'pick_your_nose':
        contentType = 'drawing_prompts';
        break;
      default:
        return res.status(400).json({ error: 'Unknown game type' });
    }
    
    const result = await GameContent.getRandomItems(gameType, contentType, 1, matureContent);
    const prompt = result.length > 0 && result[0].items.length > 0 ? result[0].items[0] : null;
    
    if (!prompt) {
      return res.status(404).json({ error: 'No prompts available' });
    }
    
    res.json({
      game_type: gameType,
      content_type: contentType,
      prompt: prompt
    });
    
  } catch (error) {
    console.error('Error fetching random prompt:', error);
    res.status(500).json({ error: 'Failed to fetch random prompt' });
  }
});

// Get random responses for Poisoning Pigeons
router.get('/poisoning_pigeons/random-responses/:count', [
  param('count').isInt({ min: 1, max: 20 }),
  query('mature_content').optional().isBoolean(),
  handleValidationErrors
], async (req, res) => {
  try {
    const count = parseInt(req.params.count);
    const matureContent = req.query.mature_content === 'true';
    
    const result = await GameContent.getRandomItems('poisoning_pigeons', 'responses', count, matureContent);
    const responses = result.length > 0 ? result[0].items : [];
    
    res.json({
      game_type: 'poisoning_pigeons',
      content_type: 'responses',
      count: responses.length,
      data: responses
    });
    
  } catch (error) {
    console.error('Error fetching random responses:', error);
    res.status(500).json({ error: 'Failed to fetch random responses' });
  }
});

module.exports = router;
