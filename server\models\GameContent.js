const mongoose = require('mongoose');

const gameContentSchema = new mongoose.Schema({
  game_type: {
    type: String,
    required: true,
    enum: ['poisoning_pigeons', 'florida_man', 'washington_path', 'pick_your_nose']
  },
  content_type: {
    type: String,
    required: true
  },
  mature_content: {
    type: Boolean,
    default: false
  },
  data: {
    type: [mongoose.Schema.Types.Mixed],
    required: true
  },
  created_at: {
    type: Date,
    default: Date.now
  },
  updated_at: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
});

// Indexes
gameContentSchema.index({ game_type: 1, content_type: 1 });
gameContentSchema.index({ mature_content: 1 });

// Static methods
gameContentSchema.statics.getContent = function(gameType, contentType, matureContent = false) {
  return this.findOne({
    game_type: gameType,
    content_type: contentType,
    mature_content: { $lte: matureContent }
  });
};

gameContentSchema.statics.getRandomItems = function(gameType, contentType, count, matureContent = false) {
  return this.aggregate([
    {
      $match: {
        game_type: gameType,
        content_type: contentType,
        mature_content: { $lte: matureContent }
      }
    },
    { $unwind: '$data' },
    { $sample: { size: count } },
    { $group: { _id: null, items: { $push: '$data' } } }
  ]);
};

module.exports = mongoose.model('GameContent', gameContentSchema);
