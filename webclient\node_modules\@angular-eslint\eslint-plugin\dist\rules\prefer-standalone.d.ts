export type Options = [];
export type MessageIds = 'preferStandalone' | 'removeStandaloneFalse';
export declare const RULE_NAME = "prefer-standalone";
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageIds, [], import("../utils/create-eslint-rule").RuleDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=prefer-standalone.d.ts.map