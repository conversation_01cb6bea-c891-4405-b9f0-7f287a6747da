{"version": 3, "sources": ["../src/ngtsc/translator/src/context.ts", "../src/ngtsc/translator/src/translator.ts"], "mappings": ";;;;;;AAaM,IAAO,UAAP,MAAc;EACG;EAArB,YAAqB,aAAoB;AAApB,SAAA,cAAA;EAAuB;EAE5C,IAAI,qBAAkB;AACpB,WAAO,KAAK,cAAc,IAAI,QAAQ,KAAK,IAAI;EACjD;EAEA,IAAI,oBAAiB;AACnB,WAAO,CAAC,KAAK,cAAc,IAAI,QAAQ,IAAI,IAAI;EACjD;;;;ACfF,YAAY,OAAO;AAcnB,IAAM,kBAAkC,oBAAI,IAAoC;EAC9E,CAAG,gBAAc,OAAO,GAAG;EAC3B,CAAG,gBAAc,MAAM,GAAG;CAC3B;AAED,IAAM,mBAAmC,oBAAI,IAAsC;EACjF,CAAG,iBAAe,KAAK,IAAI;EAC3B,CAAG,iBAAe,QAAQ,GAAG;EAC7B,CAAG,iBAAe,cAAc,IAAI;EACpC,CAAG,iBAAe,YAAY,GAAG;EACjC,CAAG,iBAAe,WAAW,GAAG;EAChC,CAAG,iBAAe,QAAQ,GAAG;EAC7B,CAAG,iBAAe,QAAQ,IAAI;EAC9B,CAAG,iBAAe,WAAW,KAAK;EAClC,CAAG,iBAAe,OAAO,GAAG;EAC5B,CAAG,iBAAe,aAAa,IAAI;EACnC,CAAG,iBAAe,OAAO,GAAG;EAC5B,CAAG,iBAAe,QAAQ,GAAG;EAC7B,CAAG,iBAAe,UAAU,GAAG;EAC/B,CAAG,iBAAe,WAAW,IAAI;EACjC,CAAG,iBAAe,cAAc,KAAK;EACrC,CAAG,iBAAe,IAAI,IAAI;EAC1B,CAAG,iBAAe,MAAM,GAAG;EAC3B,CAAG,iBAAe,iBAAiB,IAAI;EACvC,CAAG,iBAAe,gBAAgB,IAAI;EACtC,CAAG,iBAAe,IAAI,IAAI;CAC3B;AAWK,IAAO,8BAAP,MAAkC;EAQ5B;EACA;EACA;EAPF;EACA;EACA;EAER,YACU,SACA,SACA,aACR,SAAuC;AAH/B,SAAA,UAAA;AACA,SAAA,UAAA;AACA,SAAA,cAAA;AAGR,SAAK,2BAA2B,QAAQ,6BAA6B;AACrE,SAAK,gCAAgC,QAAQ,kCAAkC;AAC/E,SAAK,oBAAoB,QAAQ,sBAAsB,MAAK;IAAE;EAChE;EAEA,oBAAoB,MAAwB,SAAgB;AAC1D,UAAM,UAAU,KAAK,gCACjB,QACA,KAAK,YAAc,eAAa,KAAK,IACnC,UACA;AACN,WAAO,KAAK,eACV,KAAK,QAAQ,0BACX,KAAK,MACL,KAAK,OAAO,gBAAgB,MAAM,QAAQ,kBAAkB,GAC5D,OAAO,GAET,KAAK,eAAe;EAExB;EAEA,yBAAyB,MAA6B,SAAgB;AACpE,WAAO,KAAK,eACV,KAAK,QAAQ,0BACX,KAAK,MACL,KAAK,OAAO,IAAI,CAAC,UAAU,MAAM,IAAI,GACrC,KAAK,QAAQ,YAAY,KAAK,gBAAgB,KAAK,YAAY,QAAQ,iBAAiB,CAAC,CAAC,GAE5F,KAAK,eAAe;EAExB;EAEA,oBAAoB,MAA6B,SAAgB;AAC/D,WAAO,KAAK,eACV,KAAK,QAAQ,0BACX,KAAK,KAAK,gBAAgB,MAAM,QAAQ,iBAAiB,CAAC,GAE5D,KAAK,eAAe;EAExB;EAEA,gBAAgB,MAAyB,SAAgB;AACvD,WAAO,KAAK,eACV,KAAK,QAAQ,sBACX,KAAK,MAAM,gBAAgB,MAAM,QAAQ,kBAAkB,CAAC,GAE9D,KAAK,eAAe;EAExB;EAEA,YAAY,MAAgB,SAAgB;AAC1C,WAAO,KAAK,eACV,KAAK,QAAQ,kBACX,KAAK,UAAU,gBAAgB,MAAM,OAAO,GAC5C,KAAK,QAAQ,YAAY,KAAK,gBAAgB,KAAK,UAAU,QAAQ,iBAAiB,CAAC,GACvF,KAAK,UAAU,SAAS,IACpB,KAAK,QAAQ,YACX,KAAK,gBAAgB,KAAK,WAAW,QAAQ,iBAAiB,CAAC,IAEjE,IAAI,GAEV,KAAK,eAAe;EAExB;EAEA,iBAAiB,KAAoB,UAAiB;AACpD,UAAM,aAAa,KAAK,QAAQ,iBAAiB,IAAI,IAAK;AAC1D,SAAK,kBAAkB,YAAY,IAAI,UAAU;AACjD,WAAO;EACT;EAEA,kBAAkB,MAAsB,SAAgB;AACtD,UAAM,aAAa,KAAK,QAAQ,iBAC9B,KAAK,kBAAkB,KAAK,QAAQ,iBAAiB,KAAK,IAAI,GAAG,KAAK,UAAU,GAChF,KAAK,MAAM,gBAAgB,MAAM,OAAO,CAAC;AAE3C,WAAO,QAAQ,cACX,aACA,KAAK,QAAQ,8BAA8B,UAAU;EAC3D;EAEA,kBAAkB,MAAsB,SAAgB;AACtD,UAAM,cAAc,QAAQ;AAC5B,UAAM,SAAS,KAAK,QAAQ,oBAC1B,KAAK,SAAS,gBAAgB,MAAM,WAAW,GAC/C,KAAK,MAAM,gBAAgB,MAAM,WAAW,CAAC;AAE/C,UAAM,aAAa,KAAK,QAAQ,iBAC9B,QACA,KAAK,MAAM,gBAAgB,MAAM,WAAW,CAAC;AAE/C,WAAO,QAAQ,cACX,aACA,KAAK,QAAQ,8BAA8B,UAAU;EAC3D;EAEA,mBAAmB,MAAuB,SAAgB;AACxD,UAAM,SAAS,KAAK,QAAQ,qBAC1B,KAAK,SAAS,gBAAgB,MAAM,OAAO,GAC3C,KAAK,IAAI;AAEX,WAAO,KAAK,QAAQ,iBAAiB,QAAQ,KAAK,MAAM,gBAAgB,MAAM,OAAO,CAAC;EACxF;EAEA,wBAAwB,KAA2B,SAAgB;AACjE,WAAO,KAAK,kBACV,KAAK,QAAQ,qBACX,IAAI,GAAG,gBAAgB,MAAM,OAAO,GACpC,IAAI,KAAK,IAAI,CAAC,QAAQ,IAAI,gBAAgB,MAAM,OAAO,CAAC,GACxD,IAAI,IAAI,GAEV,IAAI,UAAU;EAElB;EAEA,+BAA+B,KAAkC,SAAgB;AAC/E,WAAO,KAAK,kBACV,KAAK,+BACH,IAAI,IAAI,gBAAgB,MAAM,OAAO,GACrC,KAAK,0BAA0B,IAAI,UAAU,OAAO,CAAC,GAEvD,IAAI,UAAU;EAElB;EAEA,yBAAyB,KAA4B,SAAgB;AACnE,WAAO,KAAK,kBACV,KAAK,QAAQ,sBAAsB,KAAK,0BAA0B,KAAK,OAAO,CAAC,GAC/E,IAAI,UAAU;EAElB;EAEA,qBAAqB,KAAwB,SAAgB;AAC3D,WAAO,KAAK,QAAQ,oBAClB,IAAI,UAAU,gBAAgB,MAAM,OAAO,GAC3C,IAAI,KAAK,IAAI,CAAC,QAAQ,IAAI,gBAAgB,MAAM,OAAO,CAAC,CAAC;EAE7D;EAEA,iBAAiB,KAAoB,UAAiB;AACpD,WAAO,KAAK,kBAAkB,KAAK,QAAQ,cAAc,IAAI,KAAK,GAAG,IAAI,UAAU;EACrF;EAEA,qBAAqB,KAAwB,SAAgB;AAc3D,UAAM,WAA8B,CAAC,sBAAsB,IAAI,kBAAiB,CAAE,CAAC;AACnF,UAAM,cAA6B,CAAA;AACnC,aAAS,IAAI,GAAG,IAAI,IAAI,YAAY,QAAQ,KAAK;AAC/C,YAAM,cAAc,KAAK,kBACvB,IAAI,YAAY,GAAG,gBAAgB,MAAM,OAAO,GAChD,IAAI,yBAAyB,CAAC,CAAC;AAEjC,kBAAY,KAAK,WAAW;AAC5B,eAAS,KAAK,sBAAsB,IAAI,0BAA0B,IAAI,CAAC,CAAC,CAAC;IAC3E;AAEA,UAAM,cAAc,KAAK,QAAQ,iBAAiB,WAAW;AAC7D,WAAO,KAAK,kBACV,KAAK,+BAA+B,aAAa,EAAC,UAAU,YAAW,CAAC,GACxE,IAAI,UAAU;EAElB;EAEQ,+BACN,KACA,UAAsC;AAEtC,WAAO,KAAK,2BACR,KAAK,oCAAoC,KAAK,QAAQ,IACtD,KAAK,QAAQ,qBAAqB,KAAK,QAAQ;EACrD;EAMQ,oCACN,YACA,EAAC,UAAU,YAAW,GAA+B;AAGrD,UAAM,6BAA6B,KAAK,QAAQ,UAAU;MACxD,uBAAuB;MACvB,kBAAkB;MAClB,eAAe,KAAK;KACrB;AAGD,UAAM,SAAwB,CAAA;AAC9B,UAAM,MAAqB,CAAA;AAC3B,eAAW,WAAW,UAAU;AAC9B,aAAO,KACL,KAAK,QAAQ,kBAAkB,KAAK,QAAQ,cAAc,QAAQ,MAAM,GAAG,QAAQ,KAAK,CAAC;AAE3F,UAAI,KACF,KAAK,QAAQ,kBAAkB,KAAK,QAAQ,cAAc,QAAQ,GAAG,GAAG,QAAQ,KAAK,CAAC;IAE1F;AAGA,UAAM,qBAAqB,KAAK,QAAQ;MACtC;MACA,CAAC,KAAK,QAAQ,mBAAmB,MAAM,GAAG,KAAK,QAAQ,mBAAmB,GAAG,CAAC;MACnE;IAAK;AAKlB,WAAO,KAAK,QAAQ;MAClB;MACA,CAAC,oBAAoB,GAAG,WAAW;MACxB;IAAK;EAEpB;EAEA,kBAAkB,KAAqB,UAAiB;AACtD,QAAI,IAAI,MAAM,SAAS,MAAM;AAC3B,UAAI,IAAI,MAAM,eAAe,MAAM;AACjC,cAAM,IAAI,MAAM,4CAA4C;MAC9D;AACA,aAAO,KAAK,QAAQ,UAAU;QAC5B,uBAAuB,IAAI,MAAM;QACjC,kBAAkB;QAClB,eAAe,KAAK;OACrB;IACH;AAGA,QAAI,IAAI,MAAM,eAAe,MAAM;AAEjC,aAAO,KAAK,QAAQ,UAAU;QAC5B,uBAAuB,IAAI,MAAM;QACjC,kBAAkB,IAAI,MAAM;QAC5B,eAAe,KAAK;OACrB;IACH,OAAO;AAEL,aAAO,KAAK,QAAQ,iBAAiB,IAAI,MAAM,IAAI;IACrD;EACF;EAEA,qBAAqB,KAAwB,SAAgB;AAC3D,WAAO,KAAK,QAAQ,kBAClB,IAAI,UAAU,gBAAgB,MAAM,OAAO,GAC3C,IAAI,SAAS,gBAAgB,MAAM,OAAO,GAC1C,IAAI,UAAW,gBAAgB,MAAM,OAAO,CAAC;EAEjD;EAEA,uBAAuB,KAA0B,SAAY;AAC3D,UAAM,gBACJ,OAAO,IAAI,QAAQ,WACf,KAAK,QAAQ,cAAc,IAAI,GAAG,IAClC,IAAI,IAAI,gBAAgB,MAAM,OAAO;AAC3C,QAAI,IAAI,YAAY;AAClB,WAAK,QAAQ,eAAe,eAAe,CAAG,iBAAe,IAAI,YAAY,IAAI,CAAC,CAAC;IACrF;AAEA,WAAO,KAAK,QAAQ,oBAAoB,aAAa;EACvD;EAEA,aAAa,KAAgB,SAAgB;AAC3C,WAAO,KAAK,QAAQ,sBAAsB,KAAK,IAAI,UAAU,gBAAgB,MAAM,OAAO,CAAC;EAC7F;EAEA,kBAAkB,KAAqB,SAAgB;AACrD,WAAO,KAAK,QAAQ,yBAClB,IAAI,QAAQ,MACZ,IAAI,OAAO,IAAI,CAAC,UAAU,MAAM,IAAI,GACpC,KAAK,QAAQ,YAAY,KAAK,gBAAgB,IAAI,YAAY,OAAO,CAAC,CAAC;EAE3E;EAEA,uBAAuB,KAA0B,SAAY;AAC3D,WAAO,KAAK,QAAQ,8BAClB,IAAI,OAAO,IAAI,CAAC,UAAU,MAAM,IAAI,GACpC,MAAM,QAAQ,IAAI,IAAI,IAClB,KAAK,QAAQ,YAAY,KAAK,gBAAgB,IAAI,MAAM,OAAO,CAAC,IAChE,IAAI,KAAK,gBAAgB,MAAM,OAAO,CAAC;EAE/C;EAEA,wBAAwB,KAA2B,SAAgB;AACjE,QAAI,CAAC,iBAAiB,IAAI,IAAI,QAAQ,GAAG;AACvC,YAAM,IAAI,MAAM,4BAA8B,iBAAe,IAAI,WAAW;IAC9E;AACA,WAAO,KAAK,QAAQ,uBAClB,IAAI,IAAI,gBAAgB,MAAM,OAAO,GACrC,iBAAiB,IAAI,IAAI,QAAQ,GACjC,IAAI,IAAI,gBAAgB,MAAM,OAAO,CAAC;EAE1C;EAEA,kBAAkB,KAAqB,SAAgB;AACrD,WAAO,KAAK,QAAQ,qBAAqB,IAAI,SAAS,gBAAgB,MAAM,OAAO,GAAG,IAAI,IAAI;EAChG;EAEA,iBAAiB,KAAoB,SAAgB;AACnD,WAAO,KAAK,QAAQ,oBAClB,IAAI,SAAS,gBAAgB,MAAM,OAAO,GAC1C,IAAI,MAAM,gBAAgB,MAAM,OAAO,CAAC;EAE5C;EAEA,sBAAsB,KAAyB,SAAgB;AAC7D,WAAO,KAAK,QAAQ,mBAClB,IAAI,QAAQ,IAAI,CAAC,SACf,KAAK,kBAAkB,KAAK,gBAAgB,MAAM,OAAO,GAAG,IAAI,UAAU,CAAC,CAC5E;EAEL;EAEA,oBAAoB,KAAuB,SAAgB;AACzD,UAAM,aAAmD,IAAI,QAAQ,IAAI,CAAC,UAAS;AACjF,aAAO;QACL,cAAc,MAAM;QACpB,QAAQ,MAAM;QACd,OAAO,MAAM,MAAM,gBAAgB,MAAM,OAAO;;IAEpD,CAAC;AACD,WAAO,KAAK,kBAAkB,KAAK,QAAQ,oBAAoB,UAAU,GAAG,IAAI,UAAU;EAC5F;EAEA,eAAe,KAAkB,SAAgB;AAC/C,UAAM,IAAI,MAAM,yBAAyB;EAC3C;EAEA,gCAAgC,KAAmC,SAAY;AAC7E,UAAM,IAAI,MAAM,wBAAwB;EAC1C;EAEA,qBAAqB,KAA6B,UAAiB;AACjE,SAAK,kBAAkB,GAAG;AAC1B,WAAO,IAAI;EACb;EAEA,gBAAgB,KAAmB,SAAgB;AACjD,WAAO,KAAK,QAAQ,uBAAuB,IAAI,KAAK,gBAAgB,MAAM,OAAO,CAAC;EACpF;EAEA,cAAc,KAAiB,SAAgB;AAC7C,WAAO,KAAK,QAAQ,qBAAqB,IAAI,KAAK,gBAAgB,MAAM,OAAO,CAAC;EAClF;EAEA,uBAAuB,KAA0B,SAAgB;AAC/D,QAAI,CAAC,gBAAgB,IAAI,IAAI,QAAQ,GAAG;AACtC,YAAM,IAAI,MAAM,2BAA6B,gBAAc,IAAI,WAAW;IAC5E;AACA,WAAO,KAAK,QAAQ,sBAClB,gBAAgB,IAAI,IAAI,QAAQ,GAChC,IAAI,KAAK,gBAAgB,MAAM,OAAO,CAAC;EAE3C;EAEA,uBAAuB,KAA0B,SAAY;AAC3D,UAAM,SAAS,IAAI,KAAK,gBAAgB,MAAM,OAAO;AACrD,WAAO,KAAK,QAAQ,8BAA8B,MAAM;EAC1D;EAEQ,gBAAgB,YAA2B,SAAgB;AACjE,WAAO,WACJ,IAAI,CAAC,SAAS,KAAK,eAAe,MAAM,OAAO,CAAC,EAChD,OAAO,CAAC,SAAS,SAAS,MAAS;EACxC;EAEQ,kBACN,KACA,MAA8B;AAE9B,WAAO,KAAK,QAAQ,kBAAkB,KAAK,YAAY,IAAI,CAAC;EAC9D;EAEQ,eACN,WACA,iBAA+C;AAE/C,QAAI,oBAAoB,QAAW;AACjC,WAAK,QAAQ,eAAe,WAAW,eAAe;IACxD;AACA,WAAO;EACT;EAEQ,0BACN,KACA,SAAgB;AAEhB,WAAO;MACL,UAAU,IAAI,SAAS,IAAI,CAAC,MAC1B,sBAAsB;QACpB,QAAQ,EAAE;QACV,KAAK,EAAE;QACP,OAAO,EAAE,cAAc,IAAI;OAC5B,CAAC;MAEJ,aAAa,IAAI,YAAY,IAAI,CAAC,MAAM,EAAE,gBAAgB,MAAM,OAAO,CAAC;;EAE5E;;AAMF,SAAS,sBAAsB,EAC7B,QACA,KACA,MAAK,GAKN;AACC,SAAO,EAAC,QAAQ,KAAK,OAAO,YAAY,KAAK,EAAC;AAChD;AAKA,SAAS,YAAY,MAA8B;AACjD,MAAI,SAAS,MAAM;AACjB,WAAO;EACT;AACA,QAAM,EAAC,OAAO,IAAG,IAAI;AACrB,QAAM,EAAC,KAAK,QAAO,IAAI,MAAM;AAC7B,MAAI,CAAC,KAAK;AACR,WAAO;EACT;AACA,SAAO;IACL;IACA;IACA,OAAO,EAAC,QAAQ,MAAM,QAAQ,MAAM,MAAM,MAAM,QAAQ,MAAM,IAAG;IACjE,KAAK,EAAC,QAAQ,IAAI,QAAQ,MAAM,IAAI,MAAM,QAAQ,IAAI,IAAG;;AAE7D;", "names": []}