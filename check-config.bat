@echo off
title Jugshine Configuration Checker
color 0B

echo.
echo ========================================
echo    JUGSHINE CONFIGURATION CHECKER
echo ========================================
echo.

:: Check current configuration
echo [INFO] Checking current configuration...
echo.

:: Check server configuration
echo [SERVER CONFIGURATION]
if exist "server\.env" (
    echo ✓ Server .env file exists
    findstr "PORT=" server\.env
    findstr "MONGODB_URI=" server\.env
) else (
    echo ✗ Server .env file missing
    echo   Run start-jugshine.bat to create it
)
echo.

:: Check web client configuration
echo [WEB CLIENT CONFIGURATION]
if exist "webclient\src\environments\environment.ts" (
    echo ✓ Web client environment file exists
    findstr "apiUrl:" webclient\src\environments\environment.ts
    findstr "socketUrl:" webclient\src\environments\environment.ts
) else (
    echo ✗ Web client environment file missing
)
echo.

:: Check Godot configuration
echo [GODOT CLIENT CONFIGURATION]
if exist "scripts\GameSettings.gd" (
    echo ✓ Godot GameSettings file exists
    findstr "server_url" scripts\GameSettings.gd
) else (
    echo ✗ Godot GameSettings file missing
)

if exist "scripts\ServerConfig.gd" (
    echo ✓ Godot ServerConfig file exists
    findstr "DEV_SERVER_URL" scripts\ServerConfig.gd
) else (
    echo ✗ Godot ServerConfig file missing
)
echo.

:: Check dependencies
echo [DEPENDENCIES]
node --version >nul 2>&1
if errorlevel 1 (
    echo ✗ Node.js not found
) else (
    echo ✓ Node.js installed
    node --version
)

npm --version >nul 2>&1
if errorlevel 1 (
    echo ✗ npm not found
) else (
    echo ✓ npm installed
    npm --version
)

ionic --version >nul 2>&1
if errorlevel 1 (
    echo ✗ Ionic CLI not found
) else (
    echo ✓ Ionic CLI installed
    ionic --version
)

if exist "server\node_modules" (
    echo ✓ Server dependencies installed
) else (
    echo ✗ Server dependencies missing
)

if exist "webclient\node_modules" (
    echo ✓ Web client dependencies installed
) else (
    echo ✗ Web client dependencies missing
)
echo.

:: Check MongoDB
echo [DATABASE]
mongod --version >nul 2>&1
if errorlevel 1 (
    echo ✗ MongoDB not found in PATH
    echo   Install from: https://www.mongodb.com/try/download/community
) else (
    echo ✓ MongoDB installed
    mongod --version | findstr "version"
)
echo.

echo ========================================
echo    CONFIGURATION CHECK COMPLETE
echo ========================================
echo.
echo If any items show ✗, please fix them before running the game.
echo Run start-jugshine.bat to automatically set up missing items.
echo.
pause
