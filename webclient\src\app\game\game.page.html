<ion-header [translucent]="true">
  <ion-toolbar color="primary">
    <ion-title>
      {{ game?.room_code || 'Game' }}
    </ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="leaveGame()" fill="clear">
        <ion-icon name="exit-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ion-padding">
  <!-- Loading State -->
  <div *ngIf="!game" class="loading-container">
    <ion-spinner name="crescent"></ion-spinner>
    <p>Loading game...</p>
  </div>

  <!-- Game Content -->
  <div *ngIf="game" class="game-container">
    <!-- Game Header -->
    <ion-card class="game-header">
      <ion-card-content>
        <div class="game-info">
          <h2>{{ getGameTitle() }}</h2>
          <div class="game-stats">
            <ion-chip color="primary">
              <ion-label>Round {{ game.current_round }}</ion-label>
            </ion-chip>
            <ion-chip [color]="getStatusColor()">
              <ion-label>{{ game.game_status }}</ion-label>
            </ion-chip>
          </div>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Player Info -->
    <ion-card class="player-info">
      <ion-card-content>
        <div class="player-details">
          <h3>{{ player?.name }}</h3>
          <div class="player-badges">
            <ion-badge *ngIf="player?.is_leader" color="warning">Leader</ion-badge>
            <ion-badge *ngIf="isPlayerJudge()" color="secondary">Judge</ion-badge>
            <ion-badge color="success">{{ player?.score }} points</ion-badge>
          </div>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Game-Specific Content -->
    <div *ngIf="player" [ngSwitch]="game.game_type">
      <!-- Poisoning Pigeons -->
      <div *ngSwitchCase="'poisoning_pigeons'">
        <app-poisoning-pigeons
          [game]="game"
          [player]="player"
          (actionSubmitted)="onActionSubmitted($event)">
        </app-poisoning-pigeons>
      </div>

      <!-- Florida Man -->
      <div *ngSwitchCase="'florida_man'">
        <app-game-placeholder
          [game]="game"
          [player]="player"
          gameTitle="Florida Man"
          (actionSubmitted)="onActionSubmitted($event)">
        </app-game-placeholder>
      </div>

      <!-- Washington Path -->
      <div *ngSwitchCase="'washington_path'">
        <app-game-placeholder
          [game]="game"
          [player]="player"
          gameTitle="Washington Path"
          (actionSubmitted)="onActionSubmitted($event)">
        </app-game-placeholder>
      </div>

      <!-- Pick Your Own Nose -->
      <div *ngSwitchCase="'pick_your_nose'">
        <app-game-placeholder
          [game]="game"
          [player]="player"
          gameTitle="Pick Your Own Nose"
          (actionSubmitted)="onActionSubmitted($event)">
        </app-game-placeholder>
      </div>

      <!-- Unknown Game Type -->
      <div *ngSwitchDefault>
        <ion-card>
          <ion-card-content>
            <p>Unknown game type: {{ game.game_type }}</p>
          </ion-card-content>
        </ion-card>
      </div>
    </div>

    <!-- Player not loaded yet -->
    <div *ngIf="!player" class="loading-container">
      <ion-spinner name="crescent"></ion-spinner>
      <p>Loading player data...</p>
    </div>

    <!-- Players List -->
    <ion-card class="players-list">
      <ion-card-header>
        <ion-card-title>Players ({{ game.players.length }})</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-list>
          <ion-item *ngFor="let gamePlayer of game.players">
            <ion-avatar slot="start">
              <div class="player-avatar">{{ gamePlayer.name.charAt(0).toUpperCase() }}</div>
            </ion-avatar>
            <ion-label>
              <h3>{{ gamePlayer.name }}</h3>
              <p>{{ gamePlayer.score }} points</p>
            </ion-label>
            <div slot="end" class="player-badges-list">
              <ion-badge *ngIf="gamePlayer.is_leader" color="warning" size="small">Leader</ion-badge>
              <ion-badge *ngIf="game.current_judge === gamePlayer.name" color="secondary" size="small">Judge</ion-badge>
            </div>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>

    <!-- Leader Controls -->
    <ion-card *ngIf="player?.is_leader && game.game_status === 'Waiting for Players'" class="leader-controls">
      <ion-card-header>
        <ion-card-title>Game Controls</ion-card-title>
        <ion-card-subtitle>You are the game leader</ion-card-subtitle>
      </ion-card-header>
      <ion-card-content>
        <ion-button 
          expand="block" 
          (click)="startGame()"
          [disabled]="game.players.length < 2">
          <ion-icon name="play" slot="start"></ion-icon>
          Start Game
        </ion-button>
        <p *ngIf="game.players.length < 2" class="min-players-warning">
          Need at least 2 players to start
        </p>
      </ion-card-content>
    </ion-card>
  </div>

  <!-- Error Alert -->
  <ion-alert
    [isOpen]="!!errorMessage"
    header="Error"
    [message]="errorMessage"
    [buttons]="['OK']"
    (didDismiss)="clearError()">
  </ion-alert>

  <!-- Leave Game Confirmation -->
  <ion-alert
    [isOpen]="showLeaveConfirmation"
    header="Leave Game"
    message="Are you sure you want to leave the game?"
    [buttons]="[
      { text: 'Cancel', role: 'cancel' },
      { text: 'Leave', handler: () => confirmLeaveGame() }
    ]"
    (didDismiss)="showLeaveConfirmation = false">
  </ion-alert>
</ion-content>

