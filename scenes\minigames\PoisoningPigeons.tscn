[gd_scene load_steps=3 format=3 uid="uid://d6xvn8ywqkqxu"]

[ext_resource type="Script" path="res://scripts/BaseMinigame.gd" id="1_3s1vr"]
[ext_resource type="Script" path="res://scripts/PoisoningPigeons.gd" id="2_4s2vr"]

[node name="PoisoningPigeons" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("2_4s2vr")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.2, 0.1, 0.15, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -400.0
offset_top = -300.0
offset_right = 400.0
offset_bottom = 300.0

[node name="GameTitle" type="Label" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 48
text = "Poisoning Pigeons"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2
modulate = Color(1, 1, 1, 0)

[node name="GameContent" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="RoomCodeContainer" type="HBoxContainer" parent="VBoxContainer/GameContent"]
layout_mode = 2

[node name="RoomCodeLabel" type="Label" parent="VBoxContainer/GameContent/RoomCodeContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_font_sizes/font_size = 24
text = "Room Code: XXXX"
horizontal_alignment = 1

[node name="StatusContainer" type="VBoxContainer" parent="VBoxContainer/GameContent"]
layout_mode = 2

[node name="GameStatusLabel" type="Label" parent="VBoxContainer/GameContent/StatusContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = "Status: Waiting for Players"
horizontal_alignment = 1

[node name="RoundLabel" type="Label" parent="VBoxContainer/GameContent/StatusContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 18
text = "Round: 0"
horizontal_alignment = 1

[node name="TimerContainer" type="HBoxContainer" parent="VBoxContainer/GameContent"]
layout_mode = 2

[node name="TimerLabel" type="Label" parent="VBoxContainer/GameContent/TimerContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_font_sizes/font_size = 22
text = ""
horizontal_alignment = 1

[node name="PromptContainer" type="VBoxContainer" parent="VBoxContainer/GameContent"]
layout_mode = 2

[node name="PromptTitleLabel" type="Label" parent="VBoxContainer/GameContent/PromptContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = "Current Prompt:"
horizontal_alignment = 1

[node name="PromptLabel" type="Label" parent="VBoxContainer/GameContent/PromptContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 18
text = "Waiting for game to start..."
horizontal_alignment = 1
autowrap_mode = 2

[node name="ResponsesContainer" type="VBoxContainer" parent="VBoxContainer/GameContent"]
layout_mode = 2

[node name="ResponsesTitleLabel" type="Label" parent="VBoxContainer/GameContent/ResponsesContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = "Submitted Responses:"
horizontal_alignment = 1
visible = false

[node name="WinnerContainer" type="VBoxContainer" parent="VBoxContainer/GameContent"]
layout_mode = 2

[node name="WinnerLabel" type="Label" parent="VBoxContainer/GameContent/WinnerContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 24
text = ""
horizontal_alignment = 1

[node name="ScoresContainer" type="VBoxContainer" parent="VBoxContainer/GameContent"]
layout_mode = 2

[node name="ScoresTitleLabel" type="Label" parent="VBoxContainer/GameContent/ScoresContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = "Scores:"
horizontal_alignment = 1

[node name="BackButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(150, 50)
size_flags_horizontal = 4
theme_override_font_sizes/font_size = 20
text = "Back to Menu"
