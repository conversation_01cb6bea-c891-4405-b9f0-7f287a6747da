[gd_scene load_steps=2 format=3 uid="uid://c5xvn8ywqkqxt"]

[ext_resource type="Script" path="res://scripts/Settings.gd" id="1_2s0vr"]

[node name="Settings" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_2s0vr")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.12, 0.12, 0.2, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -250.0
offset_top = -150.0
offset_right = 250.0
offset_bottom = 150.0

[node name="TitleLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 48
text = "SETTINGS"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2
modulate = Color(1, 1, 1, 0)

[node name="SettingsContainer" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="NSFWContainer" type="HBoxContainer" parent="VBoxContainer/SettingsContainer"]
layout_mode = 2

[node name="NSFWLabel" type="Label" parent="VBoxContainer/SettingsContainer/NSFWContainer"]
layout_mode = 2
size_flags_horizontal = 3
theme_override_font_sizes/font_size = 24
text = "Mature Content:"
vertical_alignment = 1

[node name="NSFWCheckBox" type="CheckBox" parent="VBoxContainer/SettingsContainer/NSFWContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 20

[node name="HSeparator2" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2
modulate = Color(1, 1, 1, 0)

[node name="BackButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(150, 50)
size_flags_horizontal = 4
theme_override_font_sizes/font_size = 20
text = "Back"
