# Jugshine Game Server Launcher (PowerShell)
# Cross-platform script to start the Jugshine game server and web client

param(
    [switch]$SkipDependencies,
    [int]$ServerPort = 3000,
    [int]$ClientPort = 8100
)

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "    JUGSHINE PARTY GAME LAUNCHER" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Function to check if a command exists
function Test-Command($command) {
    try {
        Get-Command $command -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Check prerequisites
Write-Host "[INFO] Checking prerequisites..." -ForegroundColor Cyan

if (-not (Test-Command "node")) {
    Write-Host "[ERROR] Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

if (-not (Test-Command "npm")) {
    Write-Host "[ERROR] npm is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Check if Ionic CLI is installed
if (-not (Test-Command "ionic")) {
    Write-Host "[WARNING] Ionic CLI not found. Installing globally..." -ForegroundColor Yellow
    npm install -g @ionic/cli
    if ($LASTEXITCODE -ne 0) {
        Write-Host "[ERROR] Failed to install Ionic CLI" -ForegroundColor Red
        exit 1
    }
}

Write-Host "[INFO] Prerequisites check passed!" -ForegroundColor Green
Write-Host ""

# Create .env file for server if it doesn't exist
if (-not (Test-Path "server\.env")) {
    Write-Host "[INFO] Creating server .env file..." -ForegroundColor Cyan
    Copy-Item "server\.env.example" "server\.env"
    
    # Update port in .env if different from default
    if ($ServerPort -ne 3000) {
        (Get-Content "server\.env") -replace "PORT=3000", "PORT=$ServerPort" | Set-Content "server\.env"
    }
    
    Write-Host "[INFO] Server .env file created" -ForegroundColor Green
}

# Install dependencies if not skipped
if (-not $SkipDependencies) {
    # Install server dependencies
    if (-not (Test-Path "server\node_modules")) {
        Write-Host "[INFO] Installing server dependencies..." -ForegroundColor Cyan
        Push-Location "server"
        npm install
        if ($LASTEXITCODE -ne 0) {
            Write-Host "[ERROR] Failed to install server dependencies" -ForegroundColor Red
            Pop-Location
            exit 1
        }
        Pop-Location
        Write-Host "[INFO] Server dependencies installed successfully" -ForegroundColor Green
    }

    # Install web client dependencies
    if (-not (Test-Path "webclient\node_modules")) {
        Write-Host "[INFO] Installing web client dependencies..." -ForegroundColor Cyan
        Push-Location "webclient"
        npm install
        if ($LASTEXITCODE -ne 0) {
            Write-Host "[ERROR] Failed to install web client dependencies" -ForegroundColor Red
            Pop-Location
            exit 1
        }
        Pop-Location
        Write-Host "[INFO] Web client dependencies installed successfully" -ForegroundColor Green
    }
}

# Seed database content
Write-Host "[INFO] Seeding game content..." -ForegroundColor Cyan
Push-Location "server"
node scripts/seedContent.js
if ($LASTEXITCODE -ne 0) {
    Write-Host "[WARNING] Database seeding failed - make sure MongoDB is running" -ForegroundColor Yellow
    Write-Host "[INFO] You can start MongoDB with: mongod" -ForegroundColor Yellow
}
Pop-Location

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "    STARTING SERVICES" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Start the server
Write-Host "[INFO] Starting Express.js server on port $ServerPort..." -ForegroundColor Cyan
$serverJob = Start-Job -ScriptBlock {
    param($ServerPort)
    Set-Location $using:PWD
    Set-Location "server"
    $env:PORT = $ServerPort
    npm run dev
} -ArgumentList $ServerPort

# Wait for server to start
Write-Host "[INFO] Waiting for server to initialize..." -ForegroundColor Cyan
Start-Sleep -Seconds 5

# Start the web client
Write-Host "[INFO] Starting Ionic web client on port $ClientPort..." -ForegroundColor Cyan
$clientJob = Start-Job -ScriptBlock {
    param($ClientPort)
    Set-Location $using:PWD
    Set-Location "webclient"
    ionic serve --port=$ClientPort --no-open
} -ArgumentList $ClientPort

# Wait for web client to start
Start-Sleep -Seconds 3

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "    JUGSHINE IS RUNNING!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "[SUCCESS] Services are running:" -ForegroundColor Green
Write-Host ""
Write-Host "   🖥️  Server:     http://localhost:$ServerPort" -ForegroundColor White
Write-Host "   📱 Web Client: http://localhost:$ClientPort" -ForegroundColor White
Write-Host ""
Write-Host "[NEXT STEPS]" -ForegroundColor Yellow
Write-Host "   1. Open Godot and run the Jugshine project" -ForegroundColor White
Write-Host "   2. Start a 'Poisoning Pigeons' game" -ForegroundColor White
Write-Host "   3. Note the 4-letter room code" -ForegroundColor White
Write-Host "   4. Open http://localhost:$ClientPort on your phone/browser" -ForegroundColor White
Write-Host "   5. Enter the room code and your name" -ForegroundColor White
Write-Host "   6. Have fun playing!" -ForegroundColor White
Write-Host ""

# Open web client in browser
Write-Host "[INFO] Opening web client in browser..." -ForegroundColor Cyan
Start-Process "http://localhost:$ClientPort"

Write-Host ""
Write-Host "Press Ctrl+C to stop all services..." -ForegroundColor Yellow
Write-Host ""

# Monitor services
try {
    while ($true) {
        Start-Sleep -Seconds 30
        $timestamp = Get-Date -Format "HH:mm:ss"
        Write-Host "[$timestamp] Services running - Server: http://localhost:$ServerPort | Web Client: http://localhost:$ClientPort" -ForegroundColor Gray
    }
}
finally {
    # Cleanup jobs when script is terminated
    Write-Host ""
    Write-Host "[INFO] Stopping services..." -ForegroundColor Cyan
    
    if ($serverJob) {
        Stop-Job $serverJob -ErrorAction SilentlyContinue
        Remove-Job $serverJob -ErrorAction SilentlyContinue
    }
    
    if ($clientJob) {
        Stop-Job $clientJob -ErrorAction SilentlyContinue
        Remove-Job $clientJob -ErrorAction SilentlyContinue
    }
    
    Write-Host "[INFO] Services stopped" -ForegroundColor Green
}
