[gd_scene load_steps=2 format=3 uid="uid://b4xvn8ywqkqxs"]

[ext_resource type="Script" uid="uid://bld2f8mpylpb8" path="res://scripts/MainMenu.gd" id="1_1s9vr"]

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_1s9vr")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.15, 0.15, 0.25, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -250.0
offset_right = 300.0
offset_bottom = 250.0

[node name="TitleLabel" type="Label" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 64
text = "JUGSHINE"
horizontal_alignment = 1

[node name="ConnectionStatus" type="Label" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 16
text = "Checking connection..."
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2
modulate = Color(1, 1, 1, 0)

[node name="GameButtonsContainer" type="VBoxContainer" parent="VBoxContainer"]
layout_mode = 2

[node name="TopRow" type="HBoxContainer" parent="VBoxContainer/GameButtonsContainer"]
layout_mode = 2

[node name="PoisoningPigeonsButton" type="Button" parent="VBoxContainer/GameButtonsContainer/TopRow"]
layout_mode = 2
size_flags_horizontal = 3
custom_minimum_size = Vector2(280, 120)
theme_override_font_sizes/font_size = 18
text = "Poisoning Pigeons"

[node name="FloridaManButton" type="Button" parent="VBoxContainer/GameButtonsContainer/TopRow"]
layout_mode = 2
size_flags_horizontal = 3
custom_minimum_size = Vector2(280, 120)
theme_override_font_sizes/font_size = 18
text = "Florida Man"

[node name="BottomRow" type="HBoxContainer" parent="VBoxContainer/GameButtonsContainer"]
layout_mode = 2

[node name="WashingtonPathButton" type="Button" parent="VBoxContainer/GameButtonsContainer/BottomRow"]
layout_mode = 2
size_flags_horizontal = 3
custom_minimum_size = Vector2(280, 120)
theme_override_font_sizes/font_size = 18
text = "Washington Path"

[node name="PickYourNoseButton" type="Button" parent="VBoxContainer/GameButtonsContainer/BottomRow"]
layout_mode = 2
size_flags_horizontal = 3
custom_minimum_size = Vector2(280, 120)
theme_override_font_sizes/font_size = 18
text = "Pick Your Own Nose"

[node name="HSeparator2" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2
modulate = Color(1, 1, 1, 0)

[node name="SettingsButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(200, 60)
size_flags_horizontal = 4
theme_override_font_sizes/font_size = 20
text = "Settings"
