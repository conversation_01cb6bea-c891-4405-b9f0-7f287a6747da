@echo off
title Jugshine Game Server Launcher
color 0A

echo.
echo ========================================
echo    JUGSHINE PARTY GAME LAUNCHER
echo ========================================
echo.

:: Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

:: Check if npm is installed
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not installed or not in PATH
    pause
    exit /b 1
)

:: Check if Ionic CLI is installed
ionic --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Ionic CLI not found. Installing globally...
    npm install -g @ionic/cli
    if errorlevel 1 (
        echo [ERROR] Failed to install Ionic CLI
        pause
        exit /b 1
    )
)

echo [INFO] Prerequisites check passed!
echo.

:: Create .env file for server if it doesn't exist
if not exist "server\.env" (
    echo [INFO] Creating server .env file...
    copy "server\.env.example" "server\.env" >nul
    echo [INFO] Server .env file created from example
)

:: Install server dependencies if node_modules doesn't exist
if not exist "server\node_modules" (
    echo [INFO] Installing server dependencies...
    cd server
    npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install server dependencies
        cd ..
        pause
        exit /b 1
    )
    cd ..
    echo [INFO] Server dependencies installed successfully
)

:: Install web client dependencies if node_modules doesn't exist
if not exist "webclient\node_modules" (
    echo [INFO] Installing web client dependencies...
    cd webclient
    npm install
    if errorlevel 1 (
        echo [ERROR] Failed to install web client dependencies
        cd ..
        pause
        exit /b 1
    )
    cd ..
    echo [INFO] Web client dependencies installed successfully
)

:: Check if MongoDB is running (optional check)
echo [INFO] Checking MongoDB connection...
timeout /t 2 /nobreak >nul

:: Seed database content if needed
echo [INFO] Seeding game content...
cd server
node scripts/seedContent.js
if errorlevel 1 (
    echo [WARNING] Database seeding failed - make sure MongoDB is running
    echo [INFO] You can start MongoDB with: mongod
    echo [INFO] Or install MongoDB from: https://www.mongodb.com/try/download/community
)
cd ..

echo.
echo ========================================
echo    STARTING SERVICES
echo ========================================
echo.

:: Start the server in a new window
echo [INFO] Starting Express.js server on port 3000...
start "Jugshine Server" cmd /k "cd server && npm run dev"

:: Wait a moment for server to start
echo [INFO] Waiting for server to initialize...
timeout /t 5 /nobreak >nul

:: Start the web client in a new window
echo [INFO] Starting Ionic web client on port 8100...
start "Jugshine Web Client" cmd /k "cd webclient && ionic serve --port=8100"

:: Wait a moment for web client to start
timeout /t 3 /nobreak >nul

echo.
echo ========================================
echo    JUGSHINE IS STARTING UP!
echo ========================================
echo.
echo [SUCCESS] Services are starting in separate windows:
echo.
echo   🖥️  Server:     http://localhost:3000
echo   📱 Web Client: http://localhost:8100
echo.
echo [NEXT STEPS]
echo   1. Wait for both services to fully start
echo   2. Open Godot and run the Jugshine project
echo   3. Start a "Poisoning Pigeons" game
echo   4. Note the 4-letter room code
echo   5. Open http://localhost:8100 on your phone/browser
echo   6. Enter the room code and your name
echo   7. Have fun playing!
echo.
echo [TROUBLESHOOTING]
echo   • If MongoDB errors occur, start MongoDB: mongod
echo   • If port 3000 is busy, change PORT in server/.env
echo   • If port 8100 is busy, change the port in this script
echo   • Check the server and web client windows for errors
echo.
echo Press any key to open the web client in your browser...
pause >nul

:: Open web client in default browser
start http://localhost:8100

echo.
echo [INFO] Web client opened in browser
echo [INFO] Keep this window open to see status messages
echo [INFO] Close this window to stop monitoring (services will continue)
echo.

:: Optional: Monitor services (comment out if not needed)
:monitor
timeout /t 30 /nobreak >nul
echo [%time%] Services running - Server: http://localhost:3000 ^| Web Client: http://localhost:8100
goto monitor
