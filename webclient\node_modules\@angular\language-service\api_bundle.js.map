{"version": 3, "sources": ["api.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the language service package.\n */\n\nimport ts from 'typescript';\n\nexport interface PluginConfig {\n  /**\n   * If true, return only Angular results. Otherwise, return Angular + TypeScript\n   * results.\n   */\n  angularOnly: boolean;\n  /**\n   * If true, enable `strictTemplates` in Angular compiler options regardless\n   * of its value in tsconfig.json.\n   */\n  forceStrictTemplates?: true;\n\n  /**\n   * If false, disables parsing control flow blocks in the compiler. Should be used only when older\n   * versions of Angular that do not support blocks (pre-v17) used with the language service.\n   */\n  enableBlockSyntax?: false;\n\n  /**\n   * Version of `@angular/core` that was detected in the user's workspace.\n   */\n  angularCoreVersion?: string;\n\n  /**\n   * If false, disables parsing of `@let` declarations in the compiler.\n   */\n  enableLetSyntax?: false;\n\n  /**\n   * Whether selectorless is enabled.\n   */\n  enableSelectorless?: true;\n\n  /**\n   * A list of diagnostic codes that should be supressed in the language service.\n   */\n  suppressAngularDiagnosticCodes?: number[];\n}\n\nexport type GetTcbResponse = {\n  /**\n   * The filename of the SourceFile this typecheck block belongs to.\n   * The filename is entirely opaque and unstable, useful only for debugging\n   * purposes.\n   */\n  fileName: string;\n  /** The content of the SourceFile this typecheck block belongs to. */\n  content: string;\n  /**\n   * Spans over node(s) in the typecheck block corresponding to the\n   * TS code generated for template node under the current cursor position.\n   *\n   * When the cursor position is over a source for which there is no generated\n   * code, `selections` is empty.\n   */\n  selections: ts.TextSpan[];\n};\n\nexport type GetComponentLocationsForTemplateResponse = ts.DocumentSpan[];\nexport type GetTemplateLocationForComponentResponse = ts.DocumentSpan | undefined;\n\n/**\n * Function that can be invoked to show progress when computing\n * refactoring edits.\n *\n * Useful for refactorings which take a long time to compute edits for.\n */\nexport type ApplyRefactoringProgressFn = (percentage: number, updateMessage: string) => void;\n\n/** Interface describing the result for computing edits of a refactoring. */\nexport interface ApplyRefactoringResult extends Omit<ts.RefactorEditInfo, 'notApplicableReason'> {\n  errorMessage?: string;\n  warningMessage?: string;\n}\n\n/**\n * `NgLanguageService` describes an instance of an Angular language service,\n * whose API surface is a strict superset of TypeScript's language service.\n */\nexport interface NgLanguageService extends ts.LanguageService {\n  getTcb(fileName: string, position: number): GetTcbResponse | undefined;\n  getComponentLocationsForTemplate(fileName: string): GetComponentLocationsForTemplateResponse;\n  getTemplateLocationForComponent(\n    fileName: string,\n    position: number,\n  ): GetTemplateLocationForComponentResponse;\n  getTypescriptLanguageService(): ts.LanguageService;\n\n  applyRefactoring(\n    fileName: string,\n    positionOrRange: number | ts.TextRange,\n    refactorName: string,\n    reportProgress: ApplyRefactoringProgressFn,\n  ): Promise<ApplyRefactoringResult | undefined>;\n\n  hasCodeFixesForErrorCode(errorCode: number): boolean;\n}\n\nexport function isNgLanguageService(\n  ls: ts.LanguageService | NgLanguageService,\n): ls is NgLanguageService {\n  return 'getTcb' in ls;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;;;;;AAkHM,SAAU,oBACd,IAA0C;AAE1C,SAAO,YAAY;AACrB;", "names": []}