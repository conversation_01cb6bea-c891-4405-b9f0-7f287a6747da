# Jugshine - Multi-Game Party Platform

A Jackbox-style party game platform with 4 different games, built with <PERSON><PERSON> (host), Express.js/MongoDB (server), and Ionic/Angular (web client).

## 🎮 Game Overview

**Jugshine** is a party game platform featuring 4 unique games:

1. **Poisoning Pigeons** ✅ - Response selection game where players choose funny responses and a judge picks the winner
2. **Florida Man** 🚧 - Creative writing game (coming soon)
3. **Washington Path** 🚧 - Decision-making story game (coming soon)  
4. **Pick Your Own Nose** 🚧 - Drawing and guessing game (coming soon)

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Godot Client  │    │  Express Server │    │   Web Client    │
│   (Game Host)   │◄──►│   (Node.js)     │◄──►│ (Ionic/Angular) │
│                 │    │                 │    │                 │
│ • Game Logic    │    │ • REST API      │    │ • Player UI     │
│ • Room Codes    │    │ • WebSocket     │    │ • Real-time     │
│ • Scoring       │    │ • MongoDB       │    │ • Responsive    │
│ • Timers        │    │ • Game State    │    │ • Mobile-first  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- MongoDB
- Godot 4.2+
- Ionic CLI

### 1. Start the Server
```bash
cd server
npm install
cp .env.example .env
# Edit .env with your MongoDB connection
npm run dev
```

### 2. Seed Game Content
```bash
cd server
node scripts/seedContent.js
```

### 3. Run Godot Client
1. Open Godot
2. Import the project from the root directory
3. Run the project
4. Navigate to "Poisoning Pigeons" game
5. Note the 4-letter room code

### 4. Start Web Client
```bash
cd webclient
npm install
ionic serve
```

### 5. Join Game
1. Open web client in browser/mobile
2. Enter room code and player name
3. Game leader starts the game
4. Follow on-screen instructions

## 🎯 Poisoning Pigeons - Complete Implementation

### Game Flow
1. **Setup**: Godot generates 4-letter room code, creates game on server
2. **Join**: Players join via web client using room code
3. **Start**: Game leader starts the game
4. **Round**: 
   - Judge is selected (round-robin)
   - Random prompt displayed
   - Non-judge players get 5 random responses to choose from
   - 30-second timer for response submission
   - Judge sees all responses and picks winner
   - Winner gets 1 point
5. **Victory**: First to 5 points wins
6. **Repeat**: New round with new judge

### Features Implemented
- ✅ Room code generation and validation
- ✅ Real-time player management
- ✅ Judge rotation system
- ✅ Response distribution and collection
- ✅ Timer management (30-second phases)
- ✅ Scoring and win conditions
- ✅ WebSocket real-time updates
- ✅ Mobile-responsive UI
- ✅ Reconnection support
- ✅ Error handling and validation

## 📁 Project Structure

```
jugshine/
├── server/                 # Express.js backend
│   ├── models/            # MongoDB schemas
│   ├── routes/            # API endpoints
│   ├── services/          # Business logic
│   ├── scripts/           # Database seeding
│   └── server.js          # Main server file
├── webclient/             # Ionic/Angular frontend
│   ├── src/app/
│   │   ├── services/      # Game service & API
│   │   ├── components/    # Game-specific components
│   │   ├── home/          # Room join interface
│   │   └── game/          # Main game interface
│   └── package.json
├── scenes/                # Godot scenes
├── scripts/               # GDScript files
├── assets/                # Game content (prompts, responses)
└── project.godot          # Godot project file
```

## 🔧 Technical Details

### Server (Express.js + MongoDB)
- **RESTful API** for game management
- **WebSocket** for real-time communication
- **MongoDB** for game state and content storage
- **Rate limiting** and input validation
- **Game cleanup** service for inactive games

### Web Client (Ionic/Angular)
- **Standalone components** architecture
- **Reactive state management** with RxJS
- **Socket.IO client** for real-time updates
- **Mobile-first responsive design**
- **Progressive Web App** capabilities

### Godot Client (GDScript)
- **State machine** for game flow management
- **HTTP requests** for server communication
- **Timer system** for game phases
- **UI management** for different game states
- **Global navigation** system

## 🌐 API Endpoints

### Games
- `POST /api/games` - Create new game
- `GET /api/games/:roomCode` - Get game state
- `PUT /api/games/:roomCode` - Update game state
- `DELETE /api/games/:roomCode` - Close game

### Players
- `POST /api/games/:roomCode/players` - Join game
- `PUT /api/games/:roomCode/:playerName` - Update player
- `POST /api/games/:roomCode/:playerName/actions` - Submit action

### Content
- `GET /api/content/:gameType/:contentType` - Get game content
- `GET /api/content/:gameType/random-prompt` - Get random prompt

## 🎨 Game Content

### Poisoning Pigeons
- **30 prompts** - Funny questions for players to respond to
- **100+ responses** - Pre-written humorous responses
- **Content filtering** - Support for mature/family-friendly modes

### Future Games
- **Florida Man**: Scenarios and headline templates
- **Washington Path**: Story nodes and decision trees
- **Pick Your Own Nose**: Drawing prompts and categories

## 🔄 Real-time Features

### WebSocket Events
- `game-updated` - Game state changes
- `player-joined` - New player joins
- `player-left` - Player leaves
- `game-closed` - Game ended

### State Synchronization
- **Automatic reconnection** for dropped connections
- **State persistence** in localStorage
- **Conflict resolution** for simultaneous actions
- **Graceful degradation** for offline scenarios

## 🚧 Development Status

| Component | Status | Features |
|-----------|--------|----------|
| **Server** | ✅ Complete | REST API, WebSocket, MongoDB, Content Management |
| **Godot Client** | ✅ Complete | Game logic, UI, Server communication |
| **Web Client** | ✅ Complete | Room join, Game interface, Real-time updates |
| **Poisoning Pigeons** | ✅ Complete | Full game implementation |
| **Florida Man** | 🚧 Planned | Creative writing mechanics |
| **Washington Path** | 🚧 Planned | Story branching system |
| **Pick Your Own Nose** | 🚧 Planned | Drawing and guessing |

## 🎯 Next Steps

1. **Complete remaining games** - Implement Florida Man, Washington Path, Pick Your Own Nose
2. **Enhanced UI** - Add animations, sound effects, better themes
3. **Advanced features** - Spectator mode, game replays, statistics
4. **Deployment** - Production setup with Docker, CI/CD
5. **Mobile apps** - Native iOS/Android builds with Capacitor

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

**Ready to party? Start your Jugshine game today!** 🎉
