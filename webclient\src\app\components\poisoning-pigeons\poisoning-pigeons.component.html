<!-- Waiting for Players -->
<ion-card *ngIf="game.game_status === 'Waiting for Players'" class="waiting-card">
  <ion-card-content>
    <div class="waiting-content">
      <ion-icon name="people-outline" size="large" color="primary"></ion-icon>
      <h3>Waiting for Players</h3>
      <p>Share the room code <strong>{{ game.room_code }}</strong> with your friends!</p>
      <p>{{ game.players.length }} / {{ game.settings.max_players }} players joined</p>
    </div>
  </ion-card-content>
</ion-card>

<!-- Game Instructions -->
<ion-card *ngIf="game.game_status === 'Ready' || game.game_status === 'Waiting for Player Responses'" class="instructions-card">
  <ion-card-header>
    <ion-card-title>Current Prompt</ion-card-title>
  </ion-card-header>
  <ion-card-content>
    <div class="prompt-display">
      <h2>{{ game.game_data.current_prompt || 'Loading prompt...' }}</h2>
    </div>
  </ion-card-content>
</ion-card>

<!-- Player Response Selection (Non-Judge) -->
<ion-card *ngIf="game.game_status === 'Waiting for Player Responses' && !isJudge" class="response-selection-card">
  <ion-card-header>
    <ion-card-title>Choose Your Response</ion-card-title>
    <ion-card-subtitle>Pick the funniest response to the prompt</ion-card-subtitle>
  </ion-card-header>
  <ion-card-content>
    <div class="response-options">
      <ion-button
        *ngFor="let response of playerResponses; let i = index"
        expand="block"
        fill="outline"
        [color]="selectedResponseIndex === i ? 'primary' : 'medium'"
        (click)="selectResponse(i)"
        class="response-button">
        {{ response }}
      </ion-button>
    </div>
    
    <ion-button
      *ngIf="selectedResponseIndex !== null"
      expand="block"
      (click)="submitResponse()"
      [disabled]="isSubmitting"
      class="submit-button">
      <ion-spinner *ngIf="isSubmitting" name="crescent" slot="start"></ion-spinner>
      <span *ngIf="!isSubmitting">Submit Response</span>
      <span *ngIf="isSubmitting">Submitting...</span>
    </ion-button>
  </ion-card-content>
</ion-card>

<!-- Judge Waiting -->
<ion-card *ngIf="game.game_status === 'Waiting for Player Responses' && isJudge" class="judge-waiting-card">
  <ion-card-content>
    <div class="judge-waiting-content">
      <ion-icon name="gavel-outline" size="large" color="secondary"></ion-icon>
      <h3>You are the Judge!</h3>
      <p>Wait for other players to submit their responses.</p>
      <p>You'll choose the winner once everyone has responded.</p>
      
      <div class="response-progress">
        <p>{{ getSubmittedResponsesCount() }} / {{ getExpectedResponsesCount() }} responses received</p>
        <ion-progress-bar 
          [value]="getResponseProgress()" 
          color="secondary">
        </ion-progress-bar>
      </div>
    </div>
  </ion-card-content>
</ion-card>

<!-- Response Already Submitted -->
<ion-card *ngIf="game.game_status === 'Waiting for Player Responses' && !isJudge && hasSubmittedResponse()" class="submitted-card">
  <ion-card-content>
    <div class="submitted-content">
      <ion-icon name="checkmark-circle-outline" size="large" color="success"></ion-icon>
      <h3>Response Submitted!</h3>
      <p>Wait for other players to submit their responses.</p>
      <p>Your response: <strong>"{{ getPlayerSubmittedResponse() }}"</strong></p>
    </div>
  </ion-card-content>
</ion-card>

<!-- Judge Response Selection -->
<ion-card *ngIf="game.game_status === 'Ready for Judging' && isJudge" class="judge-selection-card">
  <ion-card-header>
    <ion-card-title>Choose the Winner!</ion-card-title>
    <ion-card-subtitle>Select the best response to: "{{ game.game_data.current_prompt }}"</ion-card-subtitle>
  </ion-card-header>
  <ion-card-content>
    <div class="submitted-responses">
      <ion-button
        *ngFor="let response of game.game_data.submitted_responses; let i = index"
        expand="block"
        fill="outline"
        [color]="selectedWinnerIndex === i ? 'success' : 'medium'"
        (click)="selectWinner(i)"
        class="response-button">
        "{{ response.response }}"
      </ion-button>
    </div>
    
    <ion-button
      *ngIf="selectedWinnerIndex !== null"
      expand="block"
      (click)="submitJudgment()"
      [disabled]="isSubmitting"
      color="success"
      class="submit-button">
      <ion-spinner *ngIf="isSubmitting" name="crescent" slot="start"></ion-spinner>
      <span *ngIf="!isSubmitting">Choose Winner</span>
      <span *ngIf="isSubmitting">Selecting...</span>
    </ion-button>
  </ion-card-content>
</ion-card>

<!-- Waiting for Judge -->
<ion-card *ngIf="game.game_status === 'Ready for Judging' && !isJudge" class="waiting-judge-card">
  <ion-card-content>
    <div class="waiting-judge-content">
      <ion-icon name="hourglass-outline" size="large" color="warning"></ion-icon>
      <h3>Waiting for Judge</h3>
      <p><strong>{{ game.current_judge }}</strong> is choosing the winner...</p>
      
      <div class="submitted-responses-preview">
        <h4>Submitted Responses:</h4>
        <div *ngFor="let response of game.game_data.submitted_responses" class="response-preview">
          "{{ response.response }}"
        </div>
      </div>
    </div>
  </ion-card-content>
</ion-card>

<!-- Winner Announcement -->
<ion-card *ngIf="game.game_status === 'Winner Chosen'" class="winner-card">
  <ion-card-content>
    <div class="winner-content">
      <ion-icon name="trophy-outline" size="large" color="warning"></ion-icon>
      <h3>🏆 Winner! 🏆</h3>
      <h2>{{ game.game_data.current_winner?.player_name }}</h2>
      <div class="winning-response">
        "{{ game.game_data.current_winner?.response }}"
      </div>
      <p>+1 point awarded!</p>
    </div>
  </ion-card-content>
</ion-card>

<!-- Game Complete -->
<ion-card *ngIf="isGameComplete()" class="game-complete-card">
  <ion-card-content>
    <div class="game-complete-content">
      <ion-icon name="medal-outline" size="large" color="success"></ion-icon>
      <h3>🎉 Game Complete! 🎉</h3>
      <h2>{{ getGameWinner()?.name }} Wins!</h2>
      <p>Final Score: {{ getGameWinner()?.score }} points</p>

      <div *ngIf="player?.is_leader" class="leader-options">
        <ion-button expand="block" (click)="playAgain()" color="primary">
          Play Again
        </ion-button>
        <ion-button expand="block" (click)="endGame()" fill="outline">
          End Game
        </ion-button>
      </div>

      <p *ngIf="!player?.is_leader" class="waiting-leader">
        Waiting for game leader to decide...
      </p>
    </div>
  </ion-card-content>
</ion-card>
