const express = require('express');
const { body, param, validationResult } = require('express-validator');
const Game = require('../models/Game');

const router = express.Router();

// Validation middleware
const validateRoomCode = param('roomCode').isLength({ min: 4, max: 4 }).isAlpha().toUpperCase();
const validatePlayerName = param('playerName').isLength({ min: 2, max: 20 }).trim();

const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

// Update player (for reconnection, score updates, etc.)
router.put('/:roomCode/:playerName', [
  validateRoomCode,
  validatePlayerName,
  body('connection_id').optional().isString(),
  body('last_seen').optional().isISO8601(),
  handleValidationErrors
], async (req, res) => {
  try {
    const game = await Game.findByRoomCode(req.params.roomCode);
    
    if (!game) {
      return res.status(404).json({ error: 'Game not found' });
    }
    
    const player = game.getPlayer(req.params.playerName);
    
    if (!player) {
      return res.status(404).json({ error: 'Player not found' });
    }
    
    // Update player fields
    if (req.body.connection_id !== undefined) {
      player.connection_id = req.body.connection_id;
    }
    
    if (req.body.last_seen !== undefined) {
      player.last_seen = new Date(req.body.last_seen);
    } else {
      player.last_seen = new Date();
    }
    
    await game.save();
    
    // Broadcast player update
    const io = req.app.get('io');
    io.to(game.room_code).emit('player-updated', { player, game });
    
    res.json({ player, game });
    
  } catch (error) {
    console.error('Error updating player:', error);
    res.status(500).json({ error: 'Failed to update player' });
  }
});

// Submit game action (response, vote, decision, etc.)
router.post('/:roomCode/:playerName/actions', [
  validateRoomCode,
  validatePlayerName,
  body('action_type').isIn(['submit_response', 'vote', 'judge', 'make_decision', 'submit_drawing', 'submit_guess']),
  body('data').exists(),
  handleValidationErrors
], async (req, res) => {
  try {
    const { action_type, data } = req.body;
    const game = await Game.findByRoomCode(req.params.roomCode);
    
    if (!game) {
      return res.status(404).json({ error: 'Game not found' });
    }
    
    const player = game.getPlayer(req.params.playerName);
    
    if (!player) {
      return res.status(404).json({ error: 'Player not found' });
    }
    
    // Handle different action types based on game type
    let actionResult = false;
    
    switch (game.game_type) {
      case 'poisoning_pigeons':
        actionResult = await handlePoisoningPigeonsAction(game, player, action_type, data);
        break;
      case 'florida_man':
        actionResult = await handleFloridaManAction(game, player, action_type, data);
        break;
      case 'washington_path':
        actionResult = await handleWashingtonPathAction(game, player, action_type, data);
        break;
      case 'pick_your_nose':
        actionResult = await handlePickYourNoseAction(game, player, action_type, data);
        break;
      default:
        return res.status(400).json({ error: 'Unknown game type' });
    }
    
    if (!actionResult) {
      return res.status(400).json({ error: 'Invalid action for current game state' });
    }
    
    await game.save();
    
    // Broadcast game update
    const io = req.app.get('io');
    io.to(game.room_code).emit('game-updated', game);
    
    res.json({ success: true, game });
    
  } catch (error) {
    console.error('Error handling player action:', error);
    res.status(500).json({ error: 'Failed to process action' });
  }
});

// Game-specific action handlers
async function handlePoisoningPigeonsAction(game, player, actionType, data) {
  switch (actionType) {
    case 'submit_response':
      if (game.game_status !== 'Waiting for Player Responses') return false;
      if (player.name === game.current_judge) return false; // Judge can't submit
      
      // Add response to submitted responses
      const responseData = {
        player_name: player.name,
        response: data.response,
        timestamp: new Date()
      };
      
      // Remove any existing response from this player
      game.game_data.submitted_responses = game.game_data.submitted_responses.filter(
        r => r.player_name !== player.name
      );
      
      game.game_data.submitted_responses.push(responseData);
      
      // Check if all players have responded
      const expectedResponses = game.players.length - 1; // Exclude judge
      if (game.game_data.submitted_responses.length >= expectedResponses) {
        game.game_status = 'Ready for Judging';
      }
      
      return true;
      
    case 'judge':
      if (game.game_status !== 'Ready for Judging') return false;
      if (player.name !== game.current_judge) return false;
      
      const selectedResponse = game.game_data.submitted_responses[data.response_index];
      if (!selectedResponse) return false;
      
      // Award point to winner
      const winner = game.getPlayer(selectedResponse.player_name);
      if (winner) {
        winner.score += 1;
        game.game_data.current_winner = {
          player_name: winner.name,
          response: selectedResponse.response,
          round: game.current_round
        };
        
        // Add to round history
        game.rounds.push({
          round_number: game.current_round,
          winner: winner.name,
          winning_content: selectedResponse.response
        });
      }
      
      game.game_status = 'Winner Chosen';
      return true;
      
    default:
      return false;
  }
}

async function handleFloridaManAction(game, player, actionType, data) {
  // Placeholder for Florida Man game logic
  switch (actionType) {
    case 'submit_response':
      // Handle story submission
      return true;
    case 'vote':
      // Handle voting on stories
      return true;
    default:
      return false;
  }
}

async function handleWashingtonPathAction(game, player, actionType, data) {
  // Placeholder for Washington Path game logic
  switch (actionType) {
    case 'make_decision':
      // Handle decision making
      return true;
    default:
      return false;
  }
}

async function handlePickYourNoseAction(game, player, actionType, data) {
  // Placeholder for Pick Your Own Nose game logic
  switch (actionType) {
    case 'submit_drawing':
      // Handle drawing submission
      return true;
    case 'submit_guess':
      // Handle guess submission
      return true;
    default:
      return false;
  }
}

module.exports = router;
