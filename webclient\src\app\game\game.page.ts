import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { Subscription } from 'rxjs';
import { GameService, Game, Player } from '../services/game.service';
import { PoisoningPigeonsComponent } from '../components/poisoning-pigeons/poisoning-pigeons.component';
import { GamePlaceholderComponent } from '../components/game-placeholder/game-placeholder.component';

@Component({
  selector: 'app-game',
  templateUrl: './game.page.html',
  styleUrls: ['./game.page.scss'],
  imports: [CommonModule, IonicModule, PoisoningPigeonsComponent, GamePlaceholderComponent],
})
export class GamePage implements OnInit, OnDestroy {
  game: Game | null = null;
  player: Player | null = null;
  errorMessage: string | null = null;
  showLeaveConfirmation: boolean = false;

  private subscriptions: Subscription[] = [];

  constructor(
    private gameService: GameService,
    private router: Router
  ) {}

  ngOnInit() {
    // Subscribe to game state
    this.subscriptions.push(
      this.gameService.game$.subscribe(game => {
        this.game = game;
        if (!game) {
          // If game is null, redirect to home
          this.router.navigate(['/home']);
        }
      })
    );

    // Subscribe to player state
    this.subscriptions.push(
      this.gameService.player$.subscribe(player => {
        this.player = player;
      })
    );

    // Subscribe to errors
    this.subscriptions.push(
      this.gameService.error$.subscribe(error => {
        this.errorMessage = error;
      })
    );

    // If no game state, try to reconnect or redirect
    if (!this.gameService.getCurrentGame()) {
      this.gameService.reconnectToGame().then(success => {
        if (!success) {
          this.router.navigate(['/home']);
        }
      });
    }
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  getGameTitle(): string {
    if (!this.game) return 'Game';
    
    switch (this.game.game_type) {
      case 'poisoning_pigeons':
        return 'Poisoning Pigeons';
      case 'florida_man':
        return 'Florida Man';
      case 'washington_path':
        return 'Washington Path';
      case 'pick_your_nose':
        return 'Pick Your Own Nose';
      default:
        return 'Unknown Game';
    }
  }

  getStatusColor(): string {
    if (!this.game) return 'medium';
    
    switch (this.game.game_status) {
      case 'Waiting for Players':
        return 'warning';
      case 'Ready':
        return 'success';
      case 'Waiting for Player Responses':
        return 'primary';
      case 'Ready for Judging':
        return 'secondary';
      case 'Winner Chosen':
        return 'success';
      case 'Closed':
        return 'danger';
      default:
        return 'medium';
    }
  }

  isPlayerJudge(): boolean {
    return this.gameService.isPlayerJudge();
  }

  async startGame() {
    if (!this.player?.is_leader) {
      this.errorMessage = 'Only the game leader can start the game';
      return;
    }

    try {
      const success = await this.gameService.submitAction('start_game', {});
      if (!success) {
        this.errorMessage = 'Failed to start game';
      }
    } catch (error) {
      console.error('Error starting game:', error);
      this.errorMessage = 'Failed to start game';
    }
  }

  async onActionSubmitted(action: { type: string; data: any }) {
    try {
      const success = await this.gameService.submitAction(action.type, action.data);
      if (!success) {
        this.errorMessage = 'Failed to submit action';
      }
    } catch (error) {
      console.error('Error submitting action:', error);
      this.errorMessage = 'Failed to submit action';
    }
  }

  leaveGame() {
    this.showLeaveConfirmation = true;
  }

  async confirmLeaveGame() {
    try {
      await this.gameService.leaveGame();
      this.router.navigate(['/home']);
    } catch (error) {
      console.error('Error leaving game:', error);
      this.errorMessage = 'Failed to leave game';
    }
  }

  clearError() {
    this.errorMessage = null;
  }
}
