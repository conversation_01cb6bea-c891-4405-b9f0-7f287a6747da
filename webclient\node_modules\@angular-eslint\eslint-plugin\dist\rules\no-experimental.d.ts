export type Options = [];
export type MessageIds = 'noExperimental';
export declare const RULE_NAME = "no-experimental";
declare const _default: import("@typescript-eslint/utils/eslint-utils").RuleModule<"noExperimental", [], import("../utils/create-eslint-rule").RuleDocs, import("@typescript-eslint/utils/eslint-utils").RuleListener>;
export default _default;
export declare const RULE_DOCS_EXTENSION: {
    rationale: string;
};
//# sourceMappingURL=no-experimental.d.ts.map