{"name": "@angular-devkit/build-angular", "version": "20.0.4", "description": "Angular Webpack Build Facade", "main": "src/index.js", "typings": "src/index.d.ts", "builders": "builders.json", "dependencies": {"@ampproject/remapping": "2.3.0", "@angular-devkit/architect": "0.2000.4", "@angular-devkit/build-webpack": "0.2000.4", "@angular-devkit/core": "20.0.4", "@angular/build": "20.0.4", "@babel/core": "7.27.1", "@babel/generator": "7.27.1", "@babel/helper-annotate-as-pure": "7.27.1", "@babel/helper-split-export-declaration": "7.24.7", "@babel/plugin-transform-async-generator-functions": "7.27.1", "@babel/plugin-transform-async-to-generator": "7.27.1", "@babel/plugin-transform-runtime": "7.27.1", "@babel/preset-env": "7.27.2", "@babel/runtime": "7.27.1", "@discoveryjs/json-ext": "0.6.3", "@ngtools/webpack": "20.0.4", "@vitejs/plugin-basic-ssl": "2.0.0", "ansi-colors": "4.1.3", "autoprefixer": "10.4.21", "babel-loader": "10.0.0", "browserslist": "^4.21.5", "copy-webpack-plugin": "13.0.0", "css-loader": "7.1.2", "esbuild-wasm": "0.25.5", "fast-glob": "3.3.3", "http-proxy-middleware": "3.0.5", "istanbul-lib-instrument": "6.0.3", "jsonc-parser": "3.3.1", "karma-source-map-support": "1.4.0", "less": "4.3.0", "less-loader": "12.3.0", "license-webpack-plugin": "4.0.2", "loader-utils": "3.3.1", "mini-css-extract-plugin": "2.9.2", "open": "10.1.2", "ora": "8.2.0", "picomatch": "4.0.2", "piscina": "5.1.1", "postcss": "8.5.3", "postcss-loader": "8.1.1", "resolve-url-loader": "5.0.0", "rxjs": "7.8.2", "sass": "1.88.0", "sass-loader": "16.0.5", "semver": "7.7.2", "source-map-loader": "5.0.0", "source-map-support": "0.5.21", "terser": "5.39.1", "tree-kill": "1.2.2", "tslib": "2.8.1", "webpack": "5.99.8", "webpack-dev-middleware": "7.4.2", "webpack-dev-server": "5.2.1", "webpack-merge": "6.0.1", "webpack-subresource-integrity": "5.1.0"}, "optionalDependencies": {"esbuild": "0.25.5"}, "peerDependencies": {"@angular/core": "^20.0.0", "@angular/compiler-cli": "^20.0.0", "@angular/localize": "^20.0.0", "@angular/platform-browser": "^20.0.0", "@angular/platform-server": "^20.0.0", "@angular/service-worker": "^20.0.0", "@angular/ssr": "^20.0.4", "@web/test-runner": "^0.20.0", "browser-sync": "^3.0.2", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "karma": "^6.3.0", "ng-packagr": "^20.0.0", "protractor": "^7.0.0", "tailwindcss": "^2.0.0 || ^3.0.0 || ^4.0.0", "typescript": ">=5.8 <5.9"}, "peerDependenciesMeta": {"@angular/core": {"optional": true}, "@angular/localize": {"optional": true}, "@angular/platform-browser": {"optional": true}, "@angular/platform-server": {"optional": true}, "@angular/service-worker": {"optional": true}, "@angular/ssr": {"optional": true}, "@web/test-runner": {"optional": true}, "browser-sync": {"optional": true}, "jest": {"optional": true}, "jest-environment-jsdom": {"optional": true}, "karma": {"optional": true}, "ng-packagr": {"optional": true}, "protractor": {"optional": true}, "tailwindcss": {"optional": true}}, "keywords": ["Angular CLI", "Angular DevKit", "angular", "devkit", "sdk"], "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "packageManager": "pnpm@9.15.9", "engines": {"node": "^20.19.0 || ^22.12.0 || >=24.0.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "homepage": "https://github.com/angular/angular-cli"}