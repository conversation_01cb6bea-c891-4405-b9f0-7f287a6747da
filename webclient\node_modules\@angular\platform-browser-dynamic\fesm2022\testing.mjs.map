{"version": 3, "file": "testing.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/platform-browser-dynamic/testing/src/testing.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {createPlatformFactory, NgModule, PlatformRef, StaticProvider} from '@angular/core';\nimport {platformBrowserDynamic} from '../../index';\nimport {BrowserTestingModule} from '@angular/platform-browser/testing';\n\n/**\n * @deprecated Use the `platformBrowserTesting` function instead from `@angular/platform-browser/testing`.\n * In case you are not in a CLI app and rely on JIT compilation, you might also need to import `@angular/compiler`\n */\nexport const platformBrowserDynamicTesting: (extraProviders?: StaticProvider[]) => PlatformRef =\n  createPlatformFactory(platformBrowserDynamic, 'browserDynamicTesting');\n\n/**\n * NgModule for testing.\n *\n * @deprecated Use the `BrowserTestingModule` from `@angular/platform-browser/testing` instead.\n */\n@NgModule({\n  exports: [BrowserTestingModule],\n})\nexport class BrowserDynamicTestingModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAYA;;;AAGG;AACU,MAAA,6BAA6B,GACxC,qBAAqB,CAAC,sBAAsB,EAAE,uBAAuB;AAEvE;;;;AAIG;MAIU,2BAA2B,CAAA;kHAA3B,2BAA2B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAA3B,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,2BAA2B,YAF5B,oBAAoB,CAAA,EAAA,CAAA;AAEnB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,2BAA2B,YAF5B,oBAAoB,CAAA,EAAA,CAAA;;sGAEnB,2BAA2B,EAAA,UAAA,EAAA,CAAA;kBAHvC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,oBAAoB,CAAC;AAChC,iBAAA;;;;;"}