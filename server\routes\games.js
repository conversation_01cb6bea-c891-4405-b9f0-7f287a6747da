const express = require('express');
const { body, param, validationResult } = require('express-validator');
const Game = require('../models/Game');
const GameContent = require('../models/GameContent');

const router = express.Router();

// Validation middleware
const validateRoomCode = param('roomCode').isLength({ min: 4, max: 4 }).isAlpha().toUpperCase();
const validateGameType = body('game_type').isIn(['poisoning_pigeons', 'florida_man', 'washington_path', 'pick_your_nose']);

// Error handling middleware
const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }
  next();
};

// Create new game
router.post('/', [
  validateGameType,
  body('settings.mature_content').optional().isBoolean(),
  body('settings.max_players').optional().isInt({ min: 2, max: 12 }),
  body('settings.rounds_to_win').optional().isInt({ min: 1, max: 10 }),
  handleValidationErrors
], async (req, res) => {
  try {
    const { game_type, settings = {} } = req.body;
    
    // Generate unique room code
    let roomCode;
    let attempts = 0;
    do {
      roomCode = Game.generateRoomCode();
      attempts++;
      if (attempts > 10) {
        return res.status(500).json({ error: 'Unable to generate unique room code' });
      }
    } while (await Game.findByRoomCode(roomCode));
    
    // Create game
    const game = new Game({
      room_code: roomCode,
      game_type,
      settings: {
        ...settings,
        mature_content: settings.mature_content || false,
        max_players: settings.max_players || 8,
        rounds_to_win: settings.rounds_to_win || 5,
        round_time_limit: settings.round_time_limit || 30
      }
    });
    
    await game.save();
    
    // Initialize game-specific data based on game type
    switch (game.game_type) {
      case 'poisoning_pigeons':
        game.game_data = {
          current_prompt: '',
          player_responses: {},
          submitted_responses: [],
          current_winner: {}
        };
        break;
      case 'florida_man':
        game.game_data = {
          current_scenario: '',
          story_submissions: [],
          voting_results: {}
        };
        break;
      case 'washington_path':
        game.game_data = {
          story_state: {},
          decision_history: [],
          current_decision: {}
        };
        break;
      case 'pick_your_nose':
        game.game_data = {
          current_drawing_prompt: '',
          drawings: [],
          guesses: {}
        };
        break;
    }

    await game.save();
    
    res.status(201).json({
      room_code: game.room_code,
      game_type: game.game_type,
      game_status: game.game_status,
      settings: game.settings,
      created_at: game.created_at
    });
    
  } catch (error) {
    console.error('Error creating game:', error);
    res.status(500).json({ error: 'Failed to create game' });
  }
});

// Get game by room code
router.get('/:roomCode', [
  validateRoomCode,
  handleValidationErrors
], async (req, res) => {
  try {
    const game = await Game.findByRoomCode(req.params.roomCode);
    
    if (!game) {
      return res.status(404).json({ error: 'Game not found' });
    }
    
    res.json(game);
    
  } catch (error) {
    console.error('Error fetching game:', error);
    res.status(500).json({ error: 'Failed to fetch game' });
  }
});

// Update game state
router.put('/:roomCode', [
  validateRoomCode,
  handleValidationErrors
], async (req, res) => {
  try {
    const game = await Game.findByRoomCode(req.params.roomCode);
    
    if (!game) {
      return res.status(404).json({ error: 'Game not found' });
    }
    
    // Update allowed fields
    const allowedUpdates = ['game_status', 'current_phase', 'current_judge', 'current_round', 'game_data'];
    const updates = {};
    
    for (const field of allowedUpdates) {
      if (req.body[field] !== undefined) {
        updates[field] = req.body[field];
      }
    }
    
    Object.assign(game, updates);
    await game.save();
    
    // Broadcast update to all clients in the room
    const io = req.app.get('io');
    io.to(game.room_code).emit('game-updated', game);
    
    res.json(game);
    
  } catch (error) {
    console.error('Error updating game:', error);
    res.status(500).json({ error: 'Failed to update game' });
  }
});

// Delete/Close game
router.delete('/:roomCode', [
  validateRoomCode,
  handleValidationErrors
], async (req, res) => {
  try {
    const game = await Game.findByRoomCode(req.params.roomCode);
    
    if (!game) {
      return res.status(404).json({ error: 'Game not found' });
    }
    
    // Mark as closed instead of deleting
    game.game_status = 'Closed';
    await game.save();
    
    // Notify all clients
    const io = req.app.get('io');
    io.to(game.room_code).emit('game-closed', { room_code: game.room_code });
    
    res.json({ message: 'Game closed successfully' });
    
  } catch (error) {
    console.error('Error closing game:', error);
    res.status(500).json({ error: 'Failed to close game' });
  }
});

// Join game (add player)
router.post('/:roomCode/players', [
  validateRoomCode,
  body('name').isLength({ min: 2, max: 20 }).trim(),
  body('connection_id').optional().isString(),
  handleValidationErrors
], async (req, res) => {
  try {
    const { name, connection_id } = req.body;
    const game = await Game.findByRoomCode(req.params.roomCode);
    
    if (!game) {
      return res.status(404).json({ error: 'Game not found' });
    }
    
    if (game.game_status === 'Closed') {
      return res.status(400).json({ error: 'Game is closed' });
    }
    
    if (game.players.length >= game.settings.max_players) {
      return res.status(400).json({ error: 'Game is full' });
    }
    
    // Check if name is already taken
    const existingPlayer = game.getPlayer(name);
    if (existingPlayer && existingPlayer.connection_id !== connection_id) {
      return res.status(400).json({ error: 'Player name already taken' });
    }
    
    const player = game.addPlayer({ name, connection_id });
    await game.save();
    
    // Broadcast player joined
    const io = req.app.get('io');
    io.to(game.room_code).emit('player-joined', { player, game });
    
    res.status(201).json({ player, game });
    
  } catch (error) {
    console.error('Error adding player:', error);
    res.status(500).json({ error: 'Failed to add player' });
  }
});

// Leave game (remove player)
router.delete('/:roomCode/players/:playerName', [
  validateRoomCode,
  param('playerName').isLength({ min: 2, max: 20 }).trim(),
  handleValidationErrors
], async (req, res) => {
  try {
    const game = await Game.findByRoomCode(req.params.roomCode);
    
    if (!game) {
      return res.status(404).json({ error: 'Game not found' });
    }
    
    const removedPlayer = game.removePlayer(req.params.playerName);
    
    if (!removedPlayer) {
      return res.status(404).json({ error: 'Player not found' });
    }
    
    await game.save();
    
    // Broadcast player left
    const io = req.app.get('io');
    io.to(game.room_code).emit('player-left', { player: removedPlayer, game });
    
    res.json({ message: 'Player removed successfully' });
    
  } catch (error) {
    console.error('Error removing player:', error);
    res.status(500).json({ error: 'Failed to remove player' });
  }
});

module.exports = router;
