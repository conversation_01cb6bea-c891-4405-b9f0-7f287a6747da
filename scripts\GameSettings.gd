extends Node

# Global game settings
var nsfw_content_enabled: bool = false
var is_connected_to_server: bool = false
var server_url: String = "http://localhost:3000"

# Scene references
const MAIN_MENU_SCENE = "res://scenes/MainMenu.tscn"
const LOADING_SCREEN_SCENE = "res://scenes/LoadingScreen.tscn"
const SETTINGS_SCENE = "res://scenes/Settings.tscn"

# Minigame scene references
const POISONING_PIGEONS_SCENE = "res://scenes/minigames/PoisoningPigeons.tscn"
const FLORIDA_MAN_SCENE = "res://scenes/minigames/FloridaMan.tscn"
const WASHINGTON_PATH_SCENE = "res://scenes/minigames/WashingtonPath.tscn"
const PICK_YOUR_NOSE_SCENE = "res://scenes/minigames/PickYourOwnNose.tscn"

# Confirmation dialog reference
var confirmation_dialog: AcceptDialog = null

signal settings_changed

func _ready():
	# Load settings from file if it exists
	load_settings()
	
	# Set up input handling for global navigation
	set_process_input(true)

func _input(event):
	# Handle global navigation inputs (Escape, Backspace, Controller B)
	if event.is_action_pressed("return_to_menu"):
		handle_return_to_menu()

func handle_return_to_menu():
	var current_scene = get_tree().current_scene
	var scene_name = current_scene.scene_file_path
	
	# Don't show confirmation from settings scene or main menu
	if scene_name == SETTINGS_SCENE or scene_name == MAIN_MENU_SCENE:
		if scene_name == SETTINGS_SCENE:
			change_scene(MAIN_MENU_SCENE)
		return
	
	# Don't handle if we're already on loading screen
	if scene_name == LOADING_SCREEN_SCENE:
		return
	
	# Show confirmation dialog for other scenes
	show_return_confirmation()

func show_return_confirmation():
	if confirmation_dialog != null:
		confirmation_dialog.queue_free()
	
	confirmation_dialog = AcceptDialog.new()
	confirmation_dialog.dialog_text = "Return to main menu?"
	confirmation_dialog.title = "Confirm"
	
	# Add custom buttons
	confirmation_dialog.add_cancel_button("Cancel")
	var ok_button = confirmation_dialog.get_ok_button()
	ok_button.text = "Yes"
	
	# Connect signals
	confirmation_dialog.confirmed.connect(_on_return_confirmed)
	confirmation_dialog.canceled.connect(_on_return_canceled)
	
	# Add to scene and show
	get_tree().current_scene.add_child(confirmation_dialog)
	confirmation_dialog.popup_centered()

func _on_return_confirmed():
	change_scene(MAIN_MENU_SCENE)
	if confirmation_dialog:
		confirmation_dialog.queue_free()
		confirmation_dialog = null

func _on_return_canceled():
	if confirmation_dialog:
		confirmation_dialog.queue_free()
		confirmation_dialog = null

func change_scene(scene_path: String):
	get_tree().change_scene_to_file(scene_path)

func toggle_nsfw_content():
	nsfw_content_enabled = !nsfw_content_enabled
	save_settings()
	settings_changed.emit()

func save_settings():
	var config = ConfigFile.new()
	config.set_value("game", "nsfw_content_enabled", nsfw_content_enabled)
	config.save("user://game_settings.cfg")

func load_settings():
	var config = ConfigFile.new()
	var err = config.load("user://game_settings.cfg")
	
	if err == OK:
		nsfw_content_enabled = config.get_value("game", "nsfw_content_enabled", false)

# Web server connection functions
func connect_to_server():
	# This will be implemented when we create the loading screen
	# For now, just set the connection status
	is_connected_to_server = true
	print("Connected to server at: ", server_url)

func disconnect_from_server():
	is_connected_to_server = false
	print("Disconnected from server")
