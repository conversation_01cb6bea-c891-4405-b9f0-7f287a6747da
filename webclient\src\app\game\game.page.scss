.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  
  ion-spinner {
    margin-bottom: 20px;
  }
  
  p {
    color: var(--ion-color-medium);
    font-size: 1.1rem;
  }
}

.game-container {
  max-width: 600px;
  margin: 0 auto;
}

.game-header {
  margin-bottom: 16px;
  
  .game-info {
    text-align: center;
    
    h2 {
      margin: 0 0 16px 0;
      font-size: 1.5rem;
      font-weight: bold;
      color: var(--ion-color-primary);
    }
    
    .game-stats {
      display: flex;
      justify-content: center;
      gap: 8px;
      flex-wrap: wrap;
    }
  }
}

.player-info {
  margin-bottom: 16px;
  
  .player-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    h3 {
      margin: 0;
      font-size: 1.2rem;
      font-weight: 600;
    }
    
    .player-badges {
      display: flex;
      gap: 4px;
      flex-wrap: wrap;
    }
  }
}

.players-list {
  margin-bottom: 16px;
  
  .player-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--ion-color-primary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.1rem;
  }
  
  .player-badges-list {
    display: flex;
    flex-direction: column;
    gap: 4px;
    align-items: flex-end;
  }
  
  ion-item {
    --padding-start: 0;
    
    h3 {
      margin: 0 0 4px 0;
      font-weight: 600;
    }
    
    p {
      margin: 0;
      color: var(--ion-color-medium);
      font-size: 0.9rem;
    }
  }
}

.leader-controls {
  margin-bottom: 16px;
  
  .min-players-warning {
    margin: 12px 0 0 0;
    color: var(--ion-color-warning);
    font-size: 0.9rem;
    text-align: center;
  }
}

// Game-specific component containers
.game-component {
  margin-bottom: 16px;
}

// Responsive design
@media (max-width: 768px) {
  .game-container {
    padding: 0 8px;
  }
  
  .player-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .game-stats {
    justify-content: flex-start !important;
  }
}

// Dark mode adjustments
@media (prefers-color-scheme: dark) {
  .game-header,
  .player-info,
  .players-list,
  .leader-controls {
    --background: var(--ion-color-step-50);
  }
  
  .player-avatar {
    background: var(--ion-color-primary-shade);
  }
}
