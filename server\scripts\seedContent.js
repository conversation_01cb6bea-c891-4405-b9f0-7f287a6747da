const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const GameContent = require('../models/GameContent');
require('dotenv').config();

async function seedContent() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/jugshine');
    console.log('Connected to MongoDB');

    // Clear existing content
    await GameContent.deleteMany({});
    console.log('Cleared existing content');

    // Seed Poisoning Pigeons content
    await seedPoisoningPigeonsContent();
    
    // Seed other games (placeholders for now)
    await seedFloridaManContent();
    await seedWashingtonPathContent();
    await seedPickYourNoseContent();

    console.log('Content seeding completed successfully');
    process.exit(0);

  } catch (error) {
    console.error('Error seeding content:', error);
    process.exit(1);
  }
}

async function seedPoisoningPigeonsContent() {
  // Load prompts from Godot assets
  const promptsPath = path.join(__dirname, '../../assets/prompts.json');
  const responsesPath = path.join(__dirname, '../../assets/responses.json');

  if (fs.existsSync(promptsPath)) {
    const promptsData = JSON.parse(fs.readFileSync(promptsPath, 'utf8'));
    
    const promptsContent = new GameContent({
      game_type: 'poisoning_pigeons',
      content_type: 'prompts',
      mature_content: false,
      data: promptsData.prompts
    });
    
    await promptsContent.save();
    console.log(`Seeded ${promptsData.prompts.length} Poisoning Pigeons prompts`);
  }

  if (fs.existsSync(responsesPath)) {
    const responsesData = JSON.parse(fs.readFileSync(responsesPath, 'utf8'));
    
    const responsesContent = new GameContent({
      game_type: 'poisoning_pigeons',
      content_type: 'responses',
      mature_content: false,
      data: responsesData.responses
    });
    
    await responsesContent.save();
    console.log(`Seeded ${responsesData.responses.length} Poisoning Pigeons responses`);
  }
}

async function seedFloridaManContent() {
  const scenarios = [
    "Florida Man arrested for trying to teach squirrels to",
    "Florida Man calls 911 because his",
    "Florida Man found living in",
    "Florida Man attempts to pay for McDonald's with",
    "Florida Man arrested after being caught",
    "Florida Man claims he was abducted by",
    "Florida Man tries to rob bank with",
    "Florida Man found guilty of stealing",
    "Florida Man arrested for riding",
    "Florida Man calls police to report that his"
  ];

  const scenariosContent = new GameContent({
    game_type: 'florida_man',
    content_type: 'scenarios',
    mature_content: false,
    data: scenarios
  });

  await scenariosContent.save();
  console.log(`Seeded ${scenarios.length} Florida Man scenarios`);
}

async function seedWashingtonPathContent() {
  const storyNodes = [
    {
      id: 'start',
      text: "You are George Washington crossing the Delaware River. What do you do?",
      choices: [
        { id: 'choice1', text: "Rally the troops with a speech", next: 'rally' },
        { id: 'choice2', text: "Quietly proceed with the mission", next: 'stealth' }
      ]
    },
    {
      id: 'rally',
      text: "Your inspiring words boost morale! The troops are ready for battle.",
      choices: [
        { id: 'choice3', text: "Attack immediately", next: 'attack' },
        { id: 'choice4', text: "Wait for better conditions", next: 'wait' }
      ]
    },
    {
      id: 'stealth',
      text: "You successfully cross undetected. The enemy is unaware of your presence.",
      choices: [
        { id: 'choice5', text: "Launch surprise attack", next: 'surprise' },
        { id: 'choice6', text: "Gather intelligence first", next: 'intel' }
      ]
    }
  ];

  const storyContent = new GameContent({
    game_type: 'washington_path',
    content_type: 'story_nodes',
    mature_content: false,
    data: storyNodes
  });

  await storyContent.save();
  console.log(`Seeded ${storyNodes.length} Washington Path story nodes`);
}

async function seedPickYourNoseContent() {
  const drawingPrompts = [
    "A cat wearing a superhero cape",
    "A robot eating ice cream",
    "A dinosaur riding a bicycle",
    "A house made of cheese",
    "An alien playing guitar",
    "A pirate ship in space",
    "A dragon wearing glasses",
    "A tree growing upside down",
    "A fish driving a car",
    "A monkey in a business suit"
  ];

  const promptsContent = new GameContent({
    game_type: 'pick_your_nose',
    content_type: 'drawing_prompts',
    mature_content: false,
    data: drawingPrompts
  });

  await promptsContent.save();
  console.log(`Seeded ${drawingPrompts.length} Pick Your Own Nose drawing prompts`);
}

// Run the seeding
if (require.main === module) {
  seedContent();
}

module.exports = { seedContent };
