[gd_scene load_steps=2 format=3 uid="uid://dfj0262n6du66"]

[ext_resource type="Script" uid="uid://dah83oimpim2b" path="res://scripts/PickYourOwnNose.gd" id="2_7s8vr"]

[node name="PickYourOwnNose" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("2_7s8vr")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.15, 0.1, 0.2, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -300.0
offset_top = -200.0
offset_right = 300.0
offset_bottom = 200.0

[node name="GameTitle" type="Label" parent="VBoxContainer"]
layout_mode = 2
theme_override_font_sizes/font_size = 48
text = "Pick Your Own Nose"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="VBoxContainer"]
layout_mode = 2
modulate = Color(1, 1, 1, 0)

[node name="GameContent" type="Control" parent="VBoxContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="PlaceholderLabel" type="Label" parent="VBoxContainer/GameContent"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -150.0
offset_top = -25.0
offset_right = 150.0
offset_bottom = 25.0
theme_override_font_sizes/font_size = 24
text = "Game content goes here"
horizontal_alignment = 1
vertical_alignment = 1

[node name="BackButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(150, 50)
size_flags_horizontal = 4
theme_override_font_sizes/font_size = 20
text = "Back to Menu"
