<ion-header [translucent]="true">
  <ion-toolbar color="primary">
    <ion-title>
      Jugshine
    </ion-title>
  </ion-toolbar>
</ion-header>

<ion-content [fullscreen]="true" class="ion-padding">
  <div class="welcome-container">
    <div class="logo-section">
      <h1>🎮 JUGSHINE</h1>
      <p>Join the party game fun!</p>
    </div>

    <!-- Connection Status -->
    <ion-chip [color]="connectionStatus ? 'success' : 'danger'" class="connection-chip">
      <ion-icon [name]="connectionStatus ? 'wifi' : 'wifi-off'"></ion-icon>
      <ion-label>{{ connectionStatus ? 'Connected' : 'Disconnected' }}</ion-label>
    </ion-chip>

    <!-- Error Alert -->
    <ion-alert
      [isOpen]="!!errorMessage"
      header="Error"
      [message]="errorMessage"
      [buttons]="['OK']"
      (didDismiss)="clearError()">
    </ion-alert>

    <!-- Join Game Form -->
    <ion-card class="join-card">
      <ion-card-header>
        <ion-card-title>Join Game</ion-card-title>
        <ion-card-subtitle>Enter your room code and name</ion-card-subtitle>
      </ion-card-header>

      <ion-card-content>
        <form (ngSubmit)="joinGame()" #joinForm="ngForm">
          <!-- Room Code Input -->
          <ion-item>
            <ion-label position="stacked">Room Code</ion-label>
            <ion-input
              [(ngModel)]="roomCode"
              name="roomCode"
              placeholder="ABCD"
              maxlength="4"
              required
              #roomCodeInput="ngModel"
              (ionInput)="formatRoomCode($event)"
              class="room-code-input">
            </ion-input>
          </ion-item>
          <div class="validation-error" *ngIf="roomCodeInput.invalid && roomCodeInput.touched">
            Room code must be 4 letters
          </div>

          <!-- Player Name Input -->
          <ion-item>
            <ion-label position="stacked">Your Name</ion-label>
            <ion-input
              [(ngModel)]="playerName"
              name="playerName"
              placeholder="Enter your name"
              maxlength="20"
              required
              #playerNameInput="ngModel">
            </ion-input>
          </ion-item>
          <div class="validation-error" *ngIf="playerNameInput.invalid && playerNameInput.touched">
            Name is required (2-20 characters)
          </div>

          <!-- Join Button -->
          <ion-button
            expand="block"
            type="submit"
            [disabled]="!joinForm.form.valid || isJoining"
            class="join-button">
            <ion-spinner *ngIf="isJoining" name="crescent"></ion-spinner>
            <span *ngIf="!isJoining">Join Game</span>
            <span *ngIf="isJoining">Joining...</span>
          </ion-button>
        </form>
      </ion-card-content>
    </ion-card>

    <!-- Reconnect Option -->
    <ion-button
      *ngIf="canReconnect"
      fill="outline"
      expand="block"
      (click)="reconnectToGame()"
      class="reconnect-button">
      <ion-icon name="refresh" slot="start"></ion-icon>
      Reconnect to Previous Game
    </ion-button>

    <!-- Game Info -->
    <ion-card class="info-card">
      <ion-card-header>
        <ion-card-title>How to Play</ion-card-title>
      </ion-card-header>
      <ion-card-content>
        <ion-list>
          <ion-item>
            <ion-icon name="people" slot="start" color="primary"></ion-icon>
            <ion-label>
              <h3>Get the room code from the game host</h3>
              <p>The host will share a 4-letter code</p>
            </ion-label>
          </ion-item>
          <ion-item>
            <ion-icon name="person" slot="start" color="secondary"></ion-icon>
            <ion-label>
              <h3>Enter your name</h3>
              <p>Choose a fun name for the game</p>
            </ion-label>
          </ion-item>
          <ion-item>
            <ion-icon name="game-controller" slot="start" color="tertiary"></ion-icon>
            <ion-label>
              <h3>Play and have fun!</h3>
              <p>Follow the game instructions on your phone</p>
            </ion-label>
          </ion-item>
        </ion-list>
      </ion-card-content>
    </ion-card>
  </div>
</ion-content>
