{"name": "cliui", "version": "9.0.1", "description": "easily create complex multi-column command-line-interfaces", "main": "build/index.mjs", "exports": {".": "./index.mjs"}, "type": "module", "module": "./index.mjs", "scripts": {"check": "standardx '**/*.ts' && standardx '**/*.js'", "fix": "standardx --fix '**/*.ts' && standardx --fix '**/*.js'", "pretest": "rimraf build && tsc -p tsconfig.test.json", "test": "c8 mocha ./test/*.mjs", "postest": "check", "coverage": "c8 report --check-coverage", "precompile": "<PERSON><PERSON><PERSON> build", "compile": "tsc", "prepare": "npm run compile"}, "repository": "yargs/cliui", "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "author": "<PERSON> <<EMAIL>>", "license": "ISC", "dependencies": {"string-width": "^7.2.0", "strip-ansi": "^7.1.0", "wrap-ansi": "^9.0.0"}, "devDependencies": {"@types/node": "^22.13.10", "@typescript-eslint/eslint-plugin": "^4.0.0", "@typescript-eslint/parser": "^4.0.0", "c8": "^10.1.3", "chai": "^5.2.0", "chalk": "^5.4.1", "cross-env": "^7.0.2", "eslint": "^7.6.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-n": "^14.0.0", "gts": "^6.0.2", "mocha": "^11.1.0", "rimraf": "^6.0.1", "standardx": "^7.0.0", "typescript": "^5.8.2"}, "files": ["build", "index.mjs", "!*.d.ts"], "engines": {"node": ">=20"}}