# Jugshine Multi-Game Architecture

## Overview
This server supports 4 different Jackbox-style games with varying mechanics and data structures. The architecture is designed to be flexible and extensible.

## Game Types

### 1. Poisoning Pigeons (Response Selection)
- **Type**: `poisoning_pigeons`
- **Mechanics**: Judge selects best response from submitted answers
- **Data**: Prompts, multiple choice responses, scoring

### 2. <PERSON> Man (Creative Writing)
- **Type**: `florida_man`
- **Mechanics**: Players write creative headlines/stories
- **Data**: Story prompts, free-text responses, voting

### 3. Washington Path (Decision Making)
- **Type**: `washington_path`
- **Mechanics**: Players make choices that affect story progression
- **Data**: Story branches, decision points, consequences

### 4. Pick Your Own Nose (Drawing/Guessing)
- **Type**: `pick_your_nose`
- **Mechanics**: Drawing and guessing game
- **Data**: Drawing prompts, image data, guesses

## Database Schema

### Games Collection
```javascript
{
  _id: ObjectId,
  room_code: String(4), // "ABCD"
  game_type: String, // "poisoning_pigeons", "florida_man", etc.
  game_status: String, // "Waiting for Players", "Ready", etc.
  created_at: Date,
  updated_at: Date,
  
  // Game Configuration
  settings: {
    max_players: Number,
    rounds_to_win: Number,
    round_time_limit: Number,
    mature_content: Boolean
  },
  
  // Current Game State
  current_round: Number,
  current_phase: String, // Game-specific phases
  current_judge: String, // Player name (if applicable)
  
  // Players
  players: [{
    name: String,
    is_leader: Boolean,
    score: Number,
    joined_at: Date,
    last_seen: Date,
    connection_id: String // For WebSocket tracking
  }],
  
  // Game-Specific Data (flexible structure)
  game_data: {
    // For Poisoning Pigeons
    current_prompt: String,
    player_responses: Object, // {playerName: [responses]}
    submitted_responses: Array,
    
    // For Florida Man
    current_scenario: String,
    story_submissions: Array,
    voting_results: Object,
    
    // For Washington Path
    story_state: Object,
    decision_history: Array,
    current_decision: Object,
    
    // For Pick Your Own Nose
    current_drawing_prompt: String,
    drawings: Array,
    guesses: Object
  },
  
  // Round History
  rounds: [{
    round_number: Number,
    winner: String,
    winning_content: String,
    completed_at: Date
  }]
}
```

### Game Templates Collection
```javascript
{
  _id: ObjectId,
  game_type: String,
  content_type: String, // "prompts", "responses", "scenarios", etc.
  mature_content: Boolean,
  data: Array // Game-specific content
}
```

## API Structure

### Base Endpoints
- `POST /api/games` - Create new game
- `GET /api/games/:roomCode` - Get game state
- `PUT /api/games/:roomCode` - Update game state
- `DELETE /api/games/:roomCode` - Close game

### Player Management
- `POST /api/games/:roomCode/players` - Join game
- `PUT /api/games/:roomCode/players/:playerName` - Update player
- `DELETE /api/games/:roomCode/players/:playerName` - Leave game

### Game Actions (Game-Specific)
- `POST /api/games/:roomCode/actions/start` - Start game
- `POST /api/games/:roomCode/actions/submit-response` - Submit response
- `POST /api/games/:roomCode/actions/vote` - Vote/Judge
- `POST /api/games/:roomCode/actions/make-decision` - Make choice

### Real-time Communication
- WebSocket endpoint: `/ws`
- Events: `game-updated`, `player-joined`, `player-left`, `round-started`, etc.

## Game State Management

### State Machine Pattern
Each game type implements a state machine with common interfaces:
- `validateTransition(from, to)` - Check if state change is valid
- `onStateEnter(gameData)` - Handle entering new state
- `onStateExit(gameData)` - Handle leaving state
- `getAvailableActions(gameData, player)` - Get valid actions for player

### Common Game States
- `waiting_for_players`
- `ready_to_start`
- `in_progress`
- `round_complete`
- `game_complete`
- `closed`

### Game-Specific States
Each game can define additional states as needed.

## Content Management

### Dynamic Content Loading
- Game content stored in separate collections
- Support for mature/family-friendly content filtering
- Easy content updates without code changes

### Content Types by Game
- **Poisoning Pigeons**: Prompts, Responses
- **Florida Man**: Scenarios, Headlines
- **Washington Path**: Story nodes, Decisions
- **Pick Your Own Nose**: Drawing prompts, Categories

## Security & Validation

### Input Validation
- Room codes: 4 uppercase letters
- Player names: 2-20 characters, alphanumeric + spaces
- Game actions: Validated against current game state

### Rate Limiting
- API endpoints rate limited per IP
- WebSocket connections limited per room

### Data Sanitization
- All user input sanitized
- XSS protection for text content
- Image upload validation for drawing games
