{"version": 3, "file": "router.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/router/src/utils/functional_guards.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/router/src/version.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {inject, Type} from '@angular/core';\n\nimport {\n  CanActivate,\n  CanActivateChild,\n  CanActivateChildFn,\n  CanActivateFn,\n  CanDeactivate,\n  CanDeactivateFn,\n  CanMatch,\n  CanMatchFn,\n  Resolve,\n  ResolveFn,\n} from '../models';\n\n/**\n * Maps an array of injectable classes with canMatch functions to an array of equivalent\n * `CanMatchFn` for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='CanActivate'}\n *\n * @publicApi\n * @see {@link Route}\n */\nexport function mapToCanMatch(providers: Array<Type<CanMatch>>): CanMatchFn[] {\n  return providers.map(\n    (provider) =>\n      (...params) =>\n        inject(provider).canMatch(...params),\n  );\n}\n\n/**\n * Maps an array of injectable classes with canActivate functions to an array of equivalent\n * `CanActivateFn` for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='CanActivate'}\n *\n * @publicApi\n * @see {@link Route}\n */\nexport function mapToCanActivate(providers: Array<Type<CanActivate>>): CanActivateFn[] {\n  return providers.map(\n    (provider) =>\n      (...params) =>\n        inject(provider).canActivate(...params),\n  );\n}\n/**\n * Maps an array of injectable classes with canActivateChild functions to an array of equivalent\n * `CanActivateChildFn` for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='CanActivate'}\n *\n * @publicApi\n * @see {@link Route}\n */\nexport function mapToCanActivateChild(\n  providers: Array<Type<CanActivateChild>>,\n): CanActivateChildFn[] {\n  return providers.map(\n    (provider) =>\n      (...params) =>\n        inject(provider).canActivateChild(...params),\n  );\n}\n/**\n * Maps an array of injectable classes with canDeactivate functions to an array of equivalent\n * `CanDeactivateFn` for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='CanActivate'}\n *\n * @publicApi\n * @see {@link Route}\n */\nexport function mapToCanDeactivate<T = unknown>(\n  providers: Array<Type<CanDeactivate<T>>>,\n): CanDeactivateFn<T>[] {\n  return providers.map(\n    (provider) =>\n      (...params) =>\n        inject(provider).canDeactivate(...params),\n  );\n}\n/**\n * Maps an injectable class with a resolve function to an equivalent `ResolveFn`\n * for use in a `Route` definition.\n *\n * Usage {@example router/utils/functional_guards.ts region='Resolve'}\n *\n * @publicApi\n * @see {@link Route}\n */\nexport function mapToResolve<T>(provider: Type<Resolve<T>>): ResolveFn<T> {\n  return (...params) => inject(provider).resolve(...params);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the router package.\n */\n\nimport {Version} from '@angular/core';\n\n/**\n * @publicApi\n */\nexport const VERSION = new Version('20.0.6');\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAuBA;;;;;;;;AAQG;AACG,SAAU,aAAa,CAAC,SAAgC,EAAA;IAC5D,OAAO,SAAS,CAAC,GAAG,CAClB,CAAC,QAAQ,KACP,CAAC,GAAG,MAAM,KACR,MAAM,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,CACzC;AACH;AAEA;;;;;;;;AAQG;AACG,SAAU,gBAAgB,CAAC,SAAmC,EAAA;IAClE,OAAO,SAAS,CAAC,GAAG,CAClB,CAAC,QAAQ,KACP,CAAC,GAAG,MAAM,KACR,MAAM,CAAC,QAAQ,CAAC,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,CAC5C;AACH;AACA;;;;;;;;AAQG;AACG,SAAU,qBAAqB,CACnC,SAAwC,EAAA;IAExC,OAAO,SAAS,CAAC,GAAG,CAClB,CAAC,QAAQ,KACP,CAAC,GAAG,MAAM,KACR,MAAM,CAAC,QAAQ,CAAC,CAAC,gBAAgB,CAAC,GAAG,MAAM,CAAC,CACjD;AACH;AACA;;;;;;;;AAQG;AACG,SAAU,kBAAkB,CAChC,SAAwC,EAAA;IAExC,OAAO,SAAS,CAAC,GAAG,CAClB,CAAC,QAAQ,KACP,CAAC,GAAG,MAAM,KACR,MAAM,CAAC,QAAQ,CAAC,CAAC,aAAa,CAAC,GAAG,MAAM,CAAC,CAC9C;AACH;AACA;;;;;;;;AAQG;AACG,SAAU,YAAY,CAAI,QAA0B,EAAA;AACxD,IAAA,OAAO,CAAC,GAAG,MAAM,KAAK,MAAM,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC;AAC3D;;AC/FA;;;;AAIG;AAIH;;AAEG;MACU,OAAO,GAAG,IAAI,OAAO,CAAC,mBAAmB;;;;"}