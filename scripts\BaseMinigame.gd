extends Control
class_name BaseMinigame

# Base class for all minigames
# Handles common functionality like returning to main menu

@onready var game_title: Label = $VBoxContainer/GameTitle
@onready var game_content: Control = $VBoxContainer/GameContent
@onready var back_button: Button = $VBoxContainer/BackButton

var game_name: String = "Minigame"

func _ready():
	# Connect back button
	if back_button:
		back_button.pressed.connect(_on_back_pressed)
	
	# Set game title
	if game_title:
		game_title.text = game_name
	
	# Call setup function for derived classes
	setup_game()

# Override this in derived classes
func setup_game():
	pass

# Override this in derived classes to handle game-specific logic
func start_game():
	print("Starting ", game_name)

# Override this in derived classes to handle game cleanup
func end_game():
	print("Ending ", game_name)

func _on_back_pressed():
	end_game()
	GameSettings.change_scene(GameSettings.MAIN_MENU_SCENE)
